import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { LoggingEditBody } from "@/modules/logging/interface/logging.interface";
import { GetAllDesurveyResultByDrillHoleParams } from "@/modules/threed/redux/threedSlice/thunks";
import { getErrorMessage } from "@/utils/error.utils";

const downholeDataRequest = {
  validate: async (body: { projectId: string; file: File }) => {
    try {
      const response = await appRequest.upload<any>(
        `/services/app/Downhole/ValidateDownholeData`,
        [
          {
            key: "projectId",
            value: body.projectId,
          },
          {
            key: "ExcelFile",
            value: body.file,
          },
        ]
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        result: error.result,
      };
    }
  },
  getData: async (query: {
    projectId?: any;
    SuiteId?: File;
    AttributeName?: String;
    skipCount?: number;
    maxResultCount?: number;
    DrillHoleName?: string | string[];
    GeophysicsSuitId?: any;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Downhole/GetDownholeByProject`,
        query
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        message: error.result,
      };
    }
  },
  getGeologyData: async (query: {
    AttributeName?: any;
    projectId?: any;
    DrillHoleName?: any[];
    skipCount?: number;
    maxResultCount?: number;
    DepthFrom?: number;
    DepthTo?: number;
    GeologySuiteId?: number;
    Keyword?: string;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Downhole/GetGeologyData`,
        query
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        message: error.result,
      };
    }
  },
  getAllDesurveyResultByDrillHole: async (
    query: GetAllDesurveyResultByDrillHoleParams
  ) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Downhole/GetAllDesurveyResultByDrillHole`,
        query
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        message: error.result,
      };
    }
  },

  createGeologyData: async (body: {
    projectId: number;
    geologySuiteId: number;
    drillHoleName: string;
    description: string;
    depthFrom: number;
    depthTo: number;
    geologyAttribute: string;
    rockTypeId: number;
  }) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/Downhole/CreateGeologyDataPoint`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  editGeologyData: async (body: LoggingEditBody) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/Downhole/UpdateGeologyDataPoint`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  deleteGeologyData: async (body: { groupId: string }) => {
    try {
      const response = await appRequest.delete<any>(
        `/services/app/Downhole/DeleteGeologyDataPoint?groupId=${body.groupId}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getAssayData: async (query: {
    AttributeName?: any;
    projectId?: any;
    DrillHoleName?: any[];
    skipCount?: number;
    maxResultCount?: number;
    AssaySuitId?: any;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Downhole/GetAssayData`,
        query
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        message: error.result,
      };
    }
  },
  getDownholeByProject: async (query: {
    AttributeName?: any;
    projectId?: any;
    DrillHoleName?: any[];
    skipCount?: number;
    maxResultCount?: number;
    AssaySuitId?: any;
    GeophysicsSuitId?: any;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/Downhole/GetDownholeByProject`,
        query
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        message: error.result,
      };
    }
  },
  uploadGeology: async (body: {
    projectId: string;
    file: File;
    GeologySuiteId: string;
  }) => {
    try {
      const response = await appRequest.upload<any>(
        `/services/app/Downhole/UploadGeologyData`,
        [
          {
            key: "projectId",
            value: body.projectId,
          },
          {
            key: "ExcelFile",
            value: body.file,
          },
          {
            key: "GeologySuiteId",
            value: body.GeologySuiteId,
          },
        ]
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  uploadAssayData: async (body: { projectId: string; ExcelFile: File }) => {
    try {
      const response = await appRequest.upload<any>(
        `/services/app/Downhole/UploadAssayData`,
        [
          {
            key: "projectId",
            value: body.projectId,
          },
          {
            key: "ExcelFile",
            value: body.ExcelFile,
          },
        ]
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  upload: async (body: { projectId: string; file: File }) => {
    try {
      const response = await appRequest.upload<any>(
        `/services/app/Downhole/UploadDownholeData`,
        [
          {
            key: "projectId",
            value: body.projectId,
          },
          {
            key: "ExcelFile",
            value: body.file,
          },
        ]
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default downholeDataRequest;
