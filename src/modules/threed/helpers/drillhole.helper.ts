import * as THREE from "three";
import {
  DrillholeData,
  DrillholeDisplayMode,
} from "../interface/threed.interface";

export interface DrillholeGeometryConfig {
  tubeRadius: number;
  geologyTubeRadius: number;
  verticalExaggeration: number;
  resolution: number;
}

// Default configuration for drillhole visualization
export const DEFAULT_DRILLHOLE_CONFIG: DrillholeGeometryConfig = {
  tubeRadius: 1.0, // This will be overridden by traceWidth from redux state
  geologyTubeRadius: 1.5, // This will be overridden by rockGroupWidth from redux state
  verticalExaggeration: 1.0,
  resolution: 20,
};

// Default coordinate base to use as fallback
export const DEFAULT_COORDINATE_BASE = {
  east: 0,
  north: 0,
  rl: 0,
};

// Helper to normalize coordinates using provided coordinate base
export const normalizeCoordinatesWithBase = (
  east: number,
  north: number,
  rl: number,
  coordinateBase?: { east: number; north: number; rl: number } | null
): THREE.Vector3 => {
  // Validate input parameters
  if (
    typeof east !== "number" ||
    typeof north !== "number" ||
    typeof rl !== "number"
  ) {
    console.error(
      "Invalid coordinates passed to normalizeCoordinatesWithBase:",
      { east, north, rl }
    );
    return new THREE.Vector3(0, 0, 0);
  }

  // Use default coordinate base if none provided
  const base = coordinateBase || DEFAULT_COORDINATE_BASE;

  // Debug coordinate base usage
  if (!coordinateBase) {
    console.log("Using default coordinate base:", base);
  }

  // Validate coordinate base
  if (
    typeof base.east !== "number" ||
    typeof base.north !== "number" ||
    typeof base.rl !== "number"
  ) {
    console.error("Invalid coordinate base:", base);
    return new THREE.Vector3(east, north, rl);
  }

  // CORRECTED COORDINATE MAPPING:
  // East → X (horizontal, positive = right)
  // North → Y (horizontal, positive = forward)
  // RL/Elevation → Z (vertical, positive = up)
  // Note: In Three.js, Y is up by default, but we're using Z as up
  // For negative coordinates, we want to draw in the opposite direction of the 0 axis
  const normalizedPoint = new THREE.Vector3(
    east - base.east, // X: East-West (positive = east, negative = west)
    north - base.north, // Y: North-South (positive = north, negative = south)
    rl - base.rl // Z: Elevation (positive = up, negative = down)
  );

  // Debug coordinate mapping for significant coordinates
  if (Math.abs(east) > 1000 || Math.abs(north) > 1000) {
    console.log("Coordinate mapping:", {
      original: { east, north, rl },
      base: { east: base.east, north: base.north, rl: base.rl },
      normalized: {
        x: normalizedPoint.x,
        y: normalizedPoint.y,
        z: normalizedPoint.z,
      },
    });
  }

  // Debug coordinate mapping for small coordinates (like in the image data)
  if (Math.abs(east) < 10 && Math.abs(north) < 10) {
    console.log("Small coordinate mapping:", {
      original: { east, north, rl },
      base: { east: base.east, north: base.north, rl: base.rl },
      normalized: {
        x: normalizedPoint.x,
        y: normalizedPoint.y,
        z: normalizedPoint.z,
      },
    });
  }

  // Debug negative coordinates specifically
  if (east < 0 || north < 0) {
    console.log("Negative coordinate mapping:", {
      original: { east, north, rl },
      base: { east: base.east, north: base.north, rl: base.rl },
      normalized: {
        x: normalizedPoint.x,
        y: normalizedPoint.y,
        z: normalizedPoint.z,
      },
      // Show the direction relative to 0
      direction: {
        x: east < 0 ? "west" : "east",
        y: north < 0 ? "south" : "north",
        z: rl < 0 ? "down" : "up",
      },
    });
  }

  return normalizedPoint;
};

// Helper to normalize coordinates for desurvey points with Z reflection
export const normalizeDesurveyCoordinatesWithBase = (
  east: number,
  north: number,
  rl: number,
  coordinateBase?: { east: number; north: number; rl: number } | null
): THREE.Vector3 => {
  // Validate input parameters
  if (
    typeof east !== "number" ||
    typeof north !== "number" ||
    typeof rl !== "number"
  ) {
    console.error(
      "Invalid coordinates passed to normalizeDesurveyCoordinatesWithBase:",
      { east, north, rl }
    );
    return new THREE.Vector3(0, 0, 0);
  }

  // Use default coordinate base if none provided
  const base = coordinateBase || DEFAULT_COORDINATE_BASE;

  // Debug coordinate base usage
  if (!coordinateBase) {
    console.log("Using default coordinate base for desurvey:", base);
  }

  // Validate coordinate base
  if (
    typeof base.east !== "number" ||
    typeof base.north !== "number" ||
    typeof base.rl !== "number"
  ) {
    console.error("Invalid coordinate base for desurvey:", base);
    return new THREE.Vector3(east, north, rl);
  }

  // DESURVEY COORDINATE MAPPING WITH Z REFLECTION:
  // East → X (horizontal, positive = right)
  // North → Y (horizontal, positive = forward)
  // RL/Elevation → Z (vertical, negative = up, positive = down) - REFLECTED
  const normalizedPoint = new THREE.Vector3(
    east - base.east, // X: East-West (positive = east, negative = west)
    north - base.north, // Y: North-South (positive = north, negative = south)
    -(rl - base.rl) // Z: Elevation (negative = up, positive = down) - REFLECTED
  );

  // Debug desurvey coordinate mapping
  if (Math.abs(east) < 10 && Math.abs(north) < 10) {
    console.log("Desurvey coordinate mapping (Z REFLECTED):", {
      original: { east, north, rl },
      base: { east: base.east, north: base.north, rl: base.rl },
      normalized: {
        x: normalizedPoint.x,
        y: normalizedPoint.y,
        z: normalizedPoint.z,
      },
      // Show Z reflection calculation
      zReflection: {
        originalZ: rl - base.rl,
        reflectedZ: -(rl - base.rl),
      },
    });
  }

  return normalizedPoint;
};

// Previous mapping:
// Map coordinates to Three.js coordinate system:
// east -> X (horizontal)
// north -> Y (horizontal)
// rl -> Z (vertical, up positive)
// return new THREE.Vector3(east - base.east, north - base.north, rl - base.rl);

// Helper to create drillhole geometry from drillhole data (EAST, NORTH, RL, DEPTH, DIP, AZIMUTH)
export const createDrillholeGeometry = (
  drillhole: DrillholeData,
  config: DrillholeGeometryConfig = DEFAULT_DRILLHOLE_CONFIG,
  displayMode: DrillholeDisplayMode = "drillhole"
) => {
  // Debug logging
  console.log("Creating geometry for drillhole:", drillhole);

  // Validate drillhole data
  if (!drillhole) {
    console.error("Drillhole data is undefined or null");
    return new THREE.TubeGeometry(new THREE.CatmullRomCurve3([]), 1, 0.1, 8);
  }

  if (
    typeof drillhole.east !== "number" ||
    typeof drillhole.north !== "number" ||
    typeof drillhole.rl !== "number"
  ) {
    console.error("Invalid coordinates for drillhole:", drillhole.id, {
      east: drillhole.east,
      north: drillhole.north,
      rl: drillhole.rl,
    });
    return new THREE.TubeGeometry(new THREE.CatmullRomCurve3([]), 1, 0.1, 8);
  }

  // Get the coordinate base from the context if available, or use default
  const coordinateBase =
    (drillhole as any)._coordinateBase || DEFAULT_COORDINATE_BASE;

  if (!coordinateBase) {
    console.warn("No coordinate base found for drillhole", drillhole.id);
  }

  let points: THREE.Vector3[] = [];

  if (
    displayMode === "desurvey" &&
    drillhole.desurveyResults &&
    drillhole.desurveyResults.length > 0
  ) {
    // Desurvey mode: Use all desurvey points
    console.log(
      "Creating desurvey geometry for drillhole",
      drillhole.id,
      "with",
      drillhole.desurveyResults.length,
      "points"
    );
    console.log("Desurvey results:", drillhole.desurveyResults);

    // Sort desurvey results by depth (ascending)
    const sortedDesurveyResults = [...drillhole.desurveyResults].sort(
      (a, b) => a.depth - b.depth
    );

    points = sortedDesurveyResults
      .filter(
        (desurveyPoint) =>
          desurveyPoint.easting !== null &&
          desurveyPoint.northing !== null &&
          desurveyPoint.elevation !== null
      )
      .map((desurveyPoint) => {
        return normalizeDesurveyCoordinatesWithBase(
          desurveyPoint.easting!,
          desurveyPoint.northing!,
          desurveyPoint.elevation!,
          coordinateBase
        );
      });

    console.log(
      "Desurvey points for drillhole",
      drillhole.id,
      ":",
      points.length,
      "points (filtered from",
      sortedDesurveyResults.length,
      "original points)"
    );

    // If no valid desurvey points, fall back to drillhole mode
    if (points.length < 2) {
      console.warn(
        "Not enough valid desurvey points, falling back to drillhole mode for",
        drillhole.id
      );
      // Fall back to drillhole mode by creating points from drillhole data
      const topPoint = normalizeCoordinatesWithBase(
        drillhole.east,
        drillhole.north,
        drillhole.rl,
        coordinateBase
      );

      const dipRad = (90 - drillhole.dip || 0) * (Math.PI / 180);
      const azimuthRad = (drillhole.azimuth || 0) * (Math.PI / 180);
      const depthLength = drillhole.depth * config.verticalExaggeration;
      const horizontalDistance = Math.sin(dipRad) * depthLength;
      const verticalDistance = Math.cos(dipRad) * depthLength;
      const eastOffset = horizontalDistance * Math.sin(azimuthRad);
      const northOffset = horizontalDistance * Math.cos(azimuthRad);

      const bottomPoint = normalizeCoordinatesWithBase(
        drillhole.east + eastOffset,
        drillhole.north + northOffset,
        drillhole.rl - verticalDistance,
        coordinateBase
      );

      points = [topPoint, bottomPoint];
    }
  } else {
    // Drillhole mode: Use only start and end points
    console.log("Creating drillhole geometry for drillhole", drillhole.id);

    // Create normalized point for the top of the drillhole (collar)
    const topPoint = normalizeCoordinatesWithBase(
      drillhole.east,
      drillhole.north,
      drillhole.rl,
      coordinateBase
    );

    console.log("Top point for drillhole", drillhole.id, ":", topPoint);

    // Calculate the bottom point based on dip and azimuth
    // Convert dip and azimuth to radians for trigonometric calculations
    const dipRad = (90 - drillhole.dip || 0) * (Math.PI / 180); // 90-dip because 90° dip means vertical down
    const azimuthRad = (drillhole.azimuth || 0) * (Math.PI / 180);

    // Calculate the offset based on dip, azimuth and depth
    const depthLength = drillhole.depth * config.verticalExaggeration;
    const horizontalDistance = Math.sin(dipRad) * depthLength;
    const verticalDistance = Math.cos(dipRad) * depthLength;

    // Calculate the east and north offsets based on azimuth
    // Note: azimuth is measured clockwise from North (0° = North, 90° = East, 180° = South, 270° = West)
    const eastOffset = horizontalDistance * Math.sin(azimuthRad);
    const northOffset = horizontalDistance * Math.cos(azimuthRad);

    const bottomPoint = normalizeCoordinatesWithBase(
      drillhole.east + eastOffset,
      drillhole.north + northOffset,
      drillhole.rl - verticalDistance,
      coordinateBase
    );

    console.log("Bottom point for drillhole", drillhole.id, ":", bottomPoint);

    points = [topPoint, bottomPoint];
  }

  console.log("Points for drillhole", drillhole.id, ":", points);

  // Validate points before creating curve
  if (points.length < 2) {
    console.error(
      "Not enough points for drillhole",
      drillhole.id,
      ":",
      points.length,
      "points"
    );
    return new THREE.TubeGeometry(new THREE.CatmullRomCurve3([]), 1, 0.1, 8);
  }

  // Validate all points
  for (let i = 0; i < points.length; i++) {
    const point = points[i];
    if (
      !point ||
      !point.isVector3 ||
      !isFinite(point.x) ||
      !isFinite(point.y) ||
      !isFinite(point.z)
    ) {
      console.error(
        "Invalid point",
        i,
        "for drillhole",
        drillhole.id,
        ":",
        point
      );
      return new THREE.TubeGeometry(new THREE.CatmullRomCurve3([]), 1, 0.1, 8);
    }
  }

  // Create a curve from the points
  const curve = new THREE.CatmullRomCurve3(points);

  console.log("Curve created for drillhole", drillhole.id, ":", curve);

  // Validate curve before creating tube geometry
  if (!curve || !curve.points || curve.points.length === 0) {
    console.error("Invalid curve for drillhole", drillhole.id, ":", curve);
    return new THREE.TubeGeometry(new THREE.CatmullRomCurve3([]), 1, 0.1, 8);
  }

  // Validate config parameters
  const resolution = Math.max(1, Math.floor(config.resolution || 20));
  const tubeRadius = Math.max(0.1, config.tubeRadius || 1.0);

  console.log(
    "Creating tube geometry for drillhole",
    drillhole.id,
    "with config:",
    {
      resolution,
      tubeRadius,
      curve: curve.points.length,
    }
  );

  // Create a tube geometry along the curve
  return new THREE.TubeGeometry(
    curve,
    resolution,
    tubeRadius,
    8, // radial segments
    false // closed
  );
};

// Helper to calculate position along a drillhole
export const calculatePositionOnDrillhole = (
  drillhole: DrillholeData,
  depth: number,
  config: DrillholeGeometryConfig = DEFAULT_DRILLHOLE_CONFIG
): THREE.Vector3 => {
  // Scale the depth by the vertical exaggeration
  const scaledDepth = depth * config.verticalExaggeration;

  // If the drillhole is vertical (dip = 90) or dip/azimuth not provided, use the simpler calculation
  if (
    (drillhole.dip === 90 || drillhole.dip === undefined) &&
    drillhole.azimuth === undefined
  ) {
    const coordinateBase = (drillhole as any)._coordinateBase;

    if (!coordinateBase) {
      console.warn("No coordinate base found for drillhole", drillhole.id);
    }

    return normalizeCoordinatesWithBase(
      drillhole.east,
      drillhole.north,
      drillhole.rl - scaledDepth,
      coordinateBase
    );
  }

  // Calculate proportional distance along the drill hole
  const dipRad = (90 - drillhole.dip || 0) * (Math.PI / 180);
  const azimuthRad = (drillhole.azimuth || 0) * (Math.PI / 180);

  // Calculate horizontal and vertical components based on dip
  const horizontalDistance = Math.sin(dipRad) * scaledDepth;
  const verticalDistance = Math.cos(dipRad) * scaledDepth;

  // Calculate east and north offsets based on azimuth
  const eastOffset = horizontalDistance * Math.sin(azimuthRad);
  const northOffset = horizontalDistance * Math.cos(azimuthRad);

  const coordinateBase = (drillhole as any)._coordinateBase;

  return normalizeCoordinatesWithBase(
    drillhole.east + eastOffset,
    drillhole.north + northOffset,
    drillhole.rl - verticalDistance,
    coordinateBase
  );
};

// Helper to create a geometry for a drillhole segment
export const createDrillholeSegmentGeometry = (
  drillhole: DrillholeData,
  fromDepth: number,
  toDepth: number,
  config: DrillholeGeometryConfig = DEFAULT_DRILLHOLE_CONFIG
) => {
  // Create points for the segment - already normalized by calculatePositionOnDrillhole
  const points = [
    calculatePositionOnDrillhole(drillhole, fromDepth, config),
    calculatePositionOnDrillhole(drillhole, toDepth, config),
  ];

  // Create a curve from the points
  const curve = new THREE.CatmullRomCurve3(points);

  // Create a tube geometry along the curve
  return new THREE.TubeGeometry(
    curve,
    config.resolution,
    config.geologyTubeRadius,
    8, // radial segments
    false // closed
  );
};

// Helper to get normalized position for labels and UI elements
export const getNormalizedPosition = (
  drillhole: DrillholeData,
  zOffset: number = 0
): THREE.Vector3 => {
  // Position the label above the collar point
  const coordinateBase = (drillhole as any)._coordinateBase;

  return normalizeCoordinatesWithBase(
    drillhole.east,
    drillhole.north,
    drillhole.rl + zOffset,
    coordinateBase
  );
};
