"use client";
import { Environment, OrbitControls, Stats, Text } from "@react-three/drei";
import { Canvas } from "@react-three/fiber";
import React, { Suspense, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import * as THREE from "three";

// Import custom CSS for 3D view components
import "../styles/3d-view.css";

// Import components
import CameraController from "./camera-controller";
import DrillholeModel from "./canvas/drillhole-model";
import SectionPlane from "./canvas/section-plane";
import InCanvasFixedAxesHelper from "./in-canvas-fixed-axes-helper";
import RotationCenterMarker from "./rotation-center-marker";
import SceneManager from "./scene-manager";
import Upload3DFileModel from "./upload-3d-file-model";
import ViewportFixedElement from "./viewport-fixed-element";
const Canvas3D: any = Canvas;

// Import Canvas3DViewWithModal from its new file
// Import selectors and thunks
import {
  selectCamera,
  selectCoordinateBase,
  selectDrillholes,
  selectDrillholeDisplayMode,
  // Import dual layer selectors
  selectLayer,
  selectSectionConfig,
  selectSelectedDrillholes,
  selectSelectingRotationCenter,
  selectShowGrid,
  selectShowLabels,
  selectShowRotationCenterMarker,
  selectTheme,
  selectVisibilityMap,
} from "../redux/threedSlice/selectors";

// Canvas 3D View Component
const Canvas3DView: React.FC<{
  modelUrl?: string | null;
  modelCoordinates?: { east: number; north: number; rl: number };
  useFileCoordinates?: boolean;
}> = ({ modelUrl, modelCoordinates = { east: 0, north: 0, rl: 0 } }) => {
  // Use refs to maintain camera vectors across renders
  const cameraTargetRef = React.useRef<THREE.Vector3>(
    new THREE.Vector3(0, 0, 0)
  );

  // Get state selectors (wrapped in try/catch to handle case where slice is not registered yet)
  let selectedDrillholes: string[] = [];
  let showLabels = true;
  let sectionConfig: {
    enabled: boolean;
    direction: "ew" | "ns";
    position: number;
  } = {
    enabled: false,
    direction: "ew",
    position: 50,
  };
  let visibilityMap: Record<string, boolean> = {};
  let theme: "light" | "dark" = "dark";
  let showGrid = true;
  let selectingRotationCenter = false;
  let camera = {
    position: [0, 0, 0] as [number, number, number],
    target: [0, 0, 0] as [number, number, number],
  };
  let showRotationCenterMarker = true;
  let drillholesState = { data: [] as any[] };
  let drillholesData: any[] = [];
  let coordinateBase: { east: number; north: number; rl: number } | null = null;
  let drillholeDisplayMode: "drillhole" | "desurvey" = "drillhole";

  try {
    selectedDrillholes = useSelector(selectSelectedDrillholes);
    showLabels = useSelector(selectShowLabels);
    sectionConfig = useSelector(selectSectionConfig);
    theme = useSelector(selectTheme);
    showGrid = useSelector(selectShowGrid);
    visibilityMap = useSelector(selectVisibilityMap);
    selectingRotationCenter = useSelector(selectSelectingRotationCenter);
    camera = useSelector(selectCamera);
    showRotationCenterMarker = useSelector(selectShowRotationCenterMarker);
    drillholesState = useSelector(selectDrillholes);
    drillholesData = drillholesState.data || [];
    coordinateBase = useSelector(selectCoordinateBase);
    drillholeDisplayMode = useSelector(selectDrillholeDisplayMode);

    // Debug logging
    console.log("Canvas3DView - drillholesData:", drillholesData);
    console.log("Canvas3DView - coordinateBase:", coordinateBase);

    // Ensure coordinateBase is not null
    if (!coordinateBase) {
      coordinateBase = { east: 0, north: 0, rl: 0 };
    }
  } catch (error) {
    console.log(
      "Error selecting from threed slice - might not be registered yet"
    );
  }

  // Update camera target position without recreating the Vector3 object
  // This prevents the OrbitControls from resetting when the target changes
  useEffect(() => {
    // Instead of creating a new Vector3, update the existing reference
    if (camera && camera.target) {
      // Use the camera event system instead of directly modifying the controls
      // This ensures proper handling through CameraController
      const cameraEvent = new CustomEvent("camera-control", {
        detail: {
          target: [camera.target[0], camera.target[1], camera.target[2]],
        },
      });
      window.dispatchEvent(cameraEvent);

      // Also update our ref for initial rendering
      cameraTargetRef.current.set(
        camera.target[0],
        camera.target[1],
        camera.target[2]
      );
    }
  }, [camera.target]);

  // Get layer-specific settings from Redux
  const objectLayerSettings = useSelector(selectLayer("objectLayer"));
  const drillholeLayerSettings = useSelector(selectLayer("drillholeLayer"));

  return (
    <div className="threejs-canvas-container w-full h-full relative">
      <Canvas3D
        camera={{
          position: [220, 180, 50],
          fov: 50,
          up: [0, 0, 1], // Explicitly set up vector to match handleIsoView in control-panel
        }}
        shadows
        gl={{ antialias: true, preserveDrawingBuffer: true }}
        style={{ cursor: "pointer !important" }} // Force pointer cursor with !important
        className="threejs-canvas" // Add a class for CSS targeting
        // The default up axis in three.js is Y, but we're using Z as up now
        onCreated={({ gl, scene }) => {
          scene.up = new THREE.Vector3(0, 0, 1); // Set Z as up vector for the scene

          // Apply cursor styles to the canvas element with !important to override any inline styles
          const canvas = gl.domElement;
          if (canvas) {
            canvas.classList.add("threejs-canvas");
            canvas.style.setProperty("cursor", "pointer", "important");

            // Also apply the style to the parent element
            if (canvas.parentElement) {
              canvas.parentElement.style.setProperty(
                "cursor",
                "pointer",
                "important"
              );
            }
          }
        }}
      >
        {/* Camera Controls Reference */}
        <CameraController />
        <SceneManager />
        <Suspense
          fallback={
            <Text
              position={[0, 0, 0]}
              color="black"
              fontSize={5}
              anchorX="center"
              anchorY="middle"
            >
              Loading 3D models...
            </Text>
          }
        >
          <color
            attach="background"
            args={[theme === "dark" ? "#000000" : "#ffffff"]}
          />

          {/* Shared Environment for lighting */}
          <Environment preset="sunset" />

          {/* Object Layer - For 3D models */}
          <group visible={objectLayerSettings.visible}>
            {/* Object Layer Specific Lighting */}
            <ambientLight
              intensity={objectLayerSettings.lighting.ambient.intensity}
              color={objectLayerSettings.lighting.ambient.color}
            />
            <directionalLight
              position={objectLayerSettings.lighting.directional.position}
              intensity={objectLayerSettings.lighting.directional.intensity}
              color={objectLayerSettings.lighting.directional.color}
              castShadow
            />

            {/* Render uploaded 3D model if available */}
            {modelUrl && (
              <group>
                <Upload3DFileModel
                  url={modelUrl}
                  transparency={objectLayerSettings.transparency}
                  preserveCamera={true}
                  coordinateBase={coordinateBase}
                  modelCoordinates={modelCoordinates}
                  useFileCoordinates={true}
                  // Calculate if no drillholes are visible
                  noDrillholesVisible={
                    drillholesData.filter(
                      (d) =>
                        (selectedDrillholes.includes(d.id) ||
                          selectedDrillholes.length === 0) &&
                        visibilityMap[d.id] !== false
                    ).length === 0
                  }
                />
              </group>
            )}
          </group>

          {/* Drillhole Layer - For drillholes, section planes, etc. */}
          <group visible={drillholeLayerSettings.visible}>
            {/* Drillhole Layer Specific Lighting */}
            <ambientLight
              intensity={drillholeLayerSettings.lighting.ambient.intensity}
              color={drillholeLayerSettings.lighting.ambient.color}
            />
            <directionalLight
              position={drillholeLayerSettings.lighting.directional.position}
              intensity={drillholeLayerSettings.lighting.directional.intensity}
              color={drillholeLayerSettings.lighting.directional.color}
              castShadow
            />

            {/* Apply layer transparency settings */}
            <group>
              {/* Drill hole core */}
              {(() => {
                // Check if drillholes data is still loading
                if (!drillholesData || drillholesData.length === 0) {
                  console.log("No drillholes data available yet");
                  return null;
                }

                console.log(
                  "Processing drillholes data:",
                  drillholesData.length,
                  "drillholes"
                );

                // Calculate visible drillholes once for both rendering and checking empty state
                const visibleDrillholes = drillholesData.filter(
                  (d) =>
                    d && // Ensure drillhole exists
                    d.id && // Ensure drillhole has an id
                    (selectedDrillholes.includes(d.id) ||
                      selectedDrillholes.length === 0) &&
                    visibilityMap[d.id] !== false // Show if not explicitly hidden
                );

                console.log(
                  "Visible drillholes:",
                  visibleDrillholes.length,
                  "drillholes"
                );

                // Return the drill hole components
                const validDrillholes = visibleDrillholes
                  .filter((drillhole) => drillhole && drillhole.id) // Filter out invalid drillholes
                  .filter((drillhole) => {
                    // Validate drillhole data before passing to DrillholeModel
                    if (
                      typeof drillhole.east !== "number" ||
                      typeof drillhole.north !== "number" ||
                      typeof drillhole.rl !== "number"
                    ) {
                      console.warn(
                        "Skipping drillhole with invalid coordinates:",
                        drillhole.id,
                        drillhole
                      );
                      return false;
                    }
                    return true;
                  });

                console.log(
                  "Valid drillholes for rendering:",
                  validDrillholes.length,
                  "drillholes"
                );

                return validDrillholes.map((drillhole) => {
                  // Since we're now using dynamic coordinate bases, we need to pass it to DrillholeModel
                  return (
                    <DrillholeModel
                      key={drillhole.id}
                      drillhole={drillhole}
                      showLabel={showLabels}
                      transparency={drillholeLayerSettings.transparency}
                      displayMode={drillholeDisplayMode}
                    />
                  );
                });
              })()}

              {sectionConfig.enabled && (
                <SectionPlane
                  direction={sectionConfig.direction}
                  position={sectionConfig.position}
                  transparency={drillholeLayerSettings.transparency}
                />
              )}
            </group>

            {/* Rotation center marker */}
            {showRotationCenterMarker && !selectingRotationCenter && (
              <RotationCenterMarker position={camera.target} />
            )}
          </group>

          {/* Grid - Ground simulator (conditionally rendered based on showGrid state) */}
          {/* Grid is now in the X-Y plane (forward-right plane) since Z is up in new coordinate system */}
          {showGrid && (
            <gridHelper
              args={[1000, 100]}
              position={[0, 0, 0]}
              rotation={[Math.PI / 2, 0, 0]} // Rotate grid to be in X-Y plane (90 degrees around X axis)
              renderOrder={1}
            >
              <meshBasicMaterial
                attach="material"
                transparent={true}
                opacity={theme === "dark" ? 0.3 : 1}
                depthTest={false} // Ensures grid renders on top
                color={theme === "dark" ? "#ffffff" : "#F1EFEC"}
              />
            </gridHelper>
          )}

          {/* Origin marker */}
          <mesh position={[0, 0, 0]}>
            <sphereGeometry args={[1, 16, 16]} />
            <meshStandardMaterial color="#ff0000" />
          </mesh>

          {/* Camera Controls - Use the persistent ref for target instead of creating new Vector3 */}
          <OrbitControls
            makeDefault // Marks this as the default controls that useThree() will access
            enableDamping
            dampingFactor={0.05}
            minDistance={2}
            maxDistance={1500}
            enablePan={true}
            screenSpacePanning={true}
            panSpeed={1.5}
            keyPanSpeed={20}
            keys={{
              LEFT: "ArrowLeft",
              RIGHT: "ArrowRight",
              UP: "ArrowUp",
              DOWN: "ArrowDown",
              PAGE_UP: "PageUp",
              PAGE_DOWN: "PageDown",
            }}
            target={cameraTargetRef.current}
            className="orbit-controls"
            // Disable the default cursor behavior with multiple strategies
            onChange={(e) => {
              // Ensure the cursor stays as pointer even during interactions
              if (e.target && e.target.domElement) {
                e.target.domElement.style.setProperty(
                  "cursor",
                  "pointer",
                  "important"
                );
              }
            }}
            onUpdate={(controls) => {
              // Ensure cursor style is maintained after any controls update
              if (controls.domElement) {
                controls.domElement.style.setProperty(
                  "cursor",
                  "pointer",
                  "important"
                );
              }
            }}
            // Add event listeners to control DOM element
            onPointerDown={(e) => {
              if (e.target) {
                e.target.style.setProperty("cursor", "pointer", "important");
              }
            }}
            onPointerUp={(e) => {
              if (e.target) {
                e.target.style.setProperty("cursor", "pointer", "important");
              }
            }}
            onPointerMove={(e) => {
              if (e.target) {
                e.target.style.setProperty("cursor", "pointer", "important");
              }
            }}
            // Add frustumCulled={false} to ensure models are always rendered regardless of distance
            // This is handled at child component level
          />

          {/* Add control panel within the Canvas for 3D view interactions */}
          <group position={[-100, 0, -100]}>
            {/* The control panel should be added here in the future */}
          </group>

          {/* Enable for development */}
          {process.env.NODE_ENV === "development" && <Stats />}

          {/*
          Fixed axes helper in bottom left corner - now inside the Canvas
          Using ViewportFixedElement to ensure it stays in the correct position
        */}
          <ViewportFixedElement
            corner="bottom-left"
            margin={{ x: 0.08, y: 0.08 }}
            scale={0.16} // Make the axes helper 5 times smaller (0.8 / 5 = 0.16)
          >
            <InCanvasFixedAxesHelper size={5} />
          </ViewportFixedElement>
        </Suspense>
      </Canvas3D>
    </div>
  );
};

// Memoize the Canvas3DView component to prevent unnecessary re-renders
const MemoizedCanvas3DView = React.memo(Canvas3DView);

export default MemoizedCanvas3DView;
