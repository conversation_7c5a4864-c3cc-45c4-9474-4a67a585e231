"use client";
import React, { use<PERSON>emo, useEffect, useState, useC<PERSON>back } from "react";
import * as THREE from "three";
import { Text, Billboard } from "@react-three/drei";
import { ThreeEvent } from "@react-three/fiber";
import { useSelector, useDispatch } from "react-redux";
import {
  DrillholeData,
  DrillholeDisplayMode,
} from "../../interface/threed.interface";
import {
  createDrillholeGeometry,
  DEFAULT_DRILLHOLE_CONFIG,
  getNormalizedPosition,
  calculatePositionOnDrillhole,
} from "../../helpers/drillhole.helper";
import {
  selectCoordinateBase,
  selectTheme,
  selectDrillholeStyle,
  selectGeologyData,
  selectAssayData,
  selectAssayAttributeId,
  selectAssayAttributes,
} from "../../redux/threedSlice/selectors";
import GeologyModel from "./geology-model";
import AssayModel from "./assay-model";
import { updateSelectedDrilhole } from "@/modules/logging/redux/loggingSlice";
import { getDetailDrillhole } from "@/modules/drillhole/redux/drillholeSlice/thunks";

interface DrillholeModelProps {
  drillhole: DrillholeData;
  showLabel: boolean;
  config?: typeof DEFAULT_DRILLHOLE_CONFIG;
  transparency?: number; // Add transparency prop
  displayMode?: DrillholeDisplayMode; // Add display mode prop
}

interface ContextMenuState {
  visible: boolean;
  position: { x: number; y: number };
  clickedDepth?: number; // Store the depth where the user clicked
  geologySegment?: any; // Store the clicked geology segment data
}

const DrillholeModel: React.FC<DrillholeModelProps> = ({
  drillhole,
  showLabel,
  config = DEFAULT_DRILLHOLE_CONFIG,
  transparency = 0,
  displayMode = "drillhole",
}) => {
  const dispatch = useDispatch();

  // Validate drillhole data
  if (!drillhole) {
    console.error("DrillholeModel: drillhole prop is undefined or null");
    return null;
  }

  // Validate required properties
  if (
    typeof drillhole.east !== "number" ||
    typeof drillhole.north !== "number" ||
    typeof drillhole.rl !== "number"
  ) {
    console.error(
      "DrillholeModel: drillhole has invalid coordinates:",
      drillhole.id,
      {
        east: drillhole.east,
        north: drillhole.north,
        rl: drillhole.rl,
      }
    );
    return null;
  }

  // State for context menu
  const [contextMenu, setContextMenu] = useState<ContextMenuState>({
    visible: false,
    position: { x: 0, y: 0 },
  });

  // const { data: geologyData, isLoading } = useGetGeologyData(drillhole.id);
  const geologyData = useSelector(selectGeologyData(drillhole.id))?.data || [];
  const coordinateBase = useSelector(selectCoordinateBase);
  const theme = useSelector(selectTheme);
  const drillholeStyle = useSelector(selectDrillholeStyle);
  const assayData = useSelector(selectAssayData);
  const selectedAttributeId = useSelector(selectAssayAttributeId);
  const assayAttributes = useSelector(selectAssayAttributes);

  // Override config with style settings from Redux
  const configWithStyles = {
    ...config,
    tubeRadius: drillholeStyle.traceWidth,
    geologyTubeRadius: drillholeStyle.rockGroupWidth,
  };

  // Enhance drillhole with coordinate base for the helpers
  const enhancedDrillhole = useMemo(() => {
    // Make sure we have valid coordinate base values
    if (
      !coordinateBase ||
      (coordinateBase.east === 0 &&
        coordinateBase.north === 0 &&
        coordinateBase.rl === 0)
    ) {
      console.warn("Using default coordinate base for drillhole", drillhole.id);
    }

    return {
      ...drillhole,
      _coordinateBase: coordinateBase || { east: 0, north: 0, rl: 0 },
    };
  }, [drillhole, coordinateBase]);

  // Create geometry from drillhole data
  const geometry = useMemo(() => {
    return createDrillholeGeometry(
      enhancedDrillhole,
      configWithStyles,
      displayMode
    );
  }, [enhancedDrillhole, configWithStyles, displayMode]);

  // Determine the trace color based on theme and current color
  const traceColor = useMemo(() => {
    // If in dark mode and trace color is black, use white instead
    if (theme === "dark" && drillholeStyle.traceColor === "#000000") {
      return "#ffffff";
    }
    return drillholeStyle.traceColor;
  }, [theme, drillholeStyle.traceColor]);

  // Find selected attribute name from selectedAttributeId
  const selectedAttribute = useMemo(() => {
    if (!selectedAttributeId) return null;

    const foundAttribute = assayAttributes.find(
      (attr: any) => attr.id === selectedAttributeId
    );

    return foundAttribute?.name || null;
  }, [selectedAttributeId, assayAttributes]);

  // Find assay data for this drillhole
  const drillholeAssayData = useMemo(() => {
    if (!assayData || !assayData.items) return null;

    const drillholeItem = assayData.items.find(
      (item: any) => item.drillHole === drillhole.name
    );

    return drillholeItem?.assayData || [];
  }, [assayData, drillhole.name]);

  // Function to calculate the depth along the drillhole trajectory from a 3D point
  const calculateDepthFromPoint = useCallback(
    (clickedPoint: THREE.Vector3): number => {
      try {
        // Binary search to find the depth that gives the closest position to the clicked point
        let minDepth = 0;
        let maxDepth = enhancedDrillhole.depth;
        let bestDepth = 0;
        let minDistance = Infinity;

        // Use binary search with fine granularity for accuracy
        const iterations = 50; // Adjust for precision vs performance

        for (let i = 0; i <= iterations; i++) {
          const testDepth = minDepth + (maxDepth - minDepth) * (i / iterations);
          const positionAtDepth = calculatePositionOnDrillhole(
            enhancedDrillhole,
            testDepth,
            configWithStyles
          );
          const distance = clickedPoint.distanceTo(positionAtDepth);

          if (distance < minDistance) {
            minDistance = distance;
            bestDepth = testDepth;
          }
        }

        // Refine the result with a more focused search around the best depth
        const refinementRange = (maxDepth - minDepth) / iterations;
        minDepth = Math.max(0, bestDepth - refinementRange);
        maxDepth = Math.min(
          enhancedDrillhole.depth,
          bestDepth + refinementRange
        );

        for (let i = 0; i <= 20; i++) {
          const testDepth = minDepth + (maxDepth - minDepth) * (i / 20);
          const positionAtDepth = calculatePositionOnDrillhole(
            enhancedDrillhole,
            testDepth,
            configWithStyles
          );
          const distance = clickedPoint.distanceTo(positionAtDepth);

          if (distance < minDistance) {
            minDistance = distance;
            bestDepth = testDepth;
          }
        }

        // Validate the result - if the minimum distance is too large,
        // the click might not be on the drillhole
        const maxAllowedDistance = configWithStyles.geologyTubeRadius * 3; // Allow some tolerance
        if (minDistance > maxAllowedDistance) {
          console.warn(
            `Click point is ${minDistance.toFixed(
              2
            )}m away from drillhole, using fallback calculation`
          );

          // Fallback: use simple distance from collar as before
          const collarPosition = getNormalizedPosition(enhancedDrillhole, 0);
          const distance = clickedPoint.distanceTo(collarPosition);
          return Math.min(Math.max(0, distance), enhancedDrillhole.depth);
        }

        return bestDepth;
      } catch (error) {
        console.error("Error calculating depth from point:", error);

        // Fallback calculation
        const collarPosition = getNormalizedPosition(enhancedDrillhole, 0);
        const distance = clickedPoint.distanceTo(collarPosition);
        return Math.min(Math.max(0, distance), enhancedDrillhole.depth);
      }
    },
    [enhancedDrillhole, configWithStyles]
  );

  // Context menu handlers
  const handleContextMenu = useCallback(
    (e: ThreeEvent<MouseEvent>) => {
      // With ThreeEvent, we don't need to call stopPropagation or preventDefault directly
      // as the event handling is different in React Three Fiber

      // Access the native DOM event from the ThreeEvent
      const { clientX, clientY } = e.nativeEvent;

      // Calculate the clicked depth based on the intersection point
      let clickedDepth: number | undefined;

      if (e.intersections && e.intersections.length > 0) {
        const intersection = e.intersections[0];
        const point = intersection.point;

        // Use the improved depth calculation that considers drillhole trajectory
        clickedDepth = calculateDepthFromPoint(point);

        // Validate the calculated depth
        if (clickedDepth < 0 || clickedDepth > enhancedDrillhole.depth) {
          console.warn(
            `Calculated depth ${clickedDepth?.toFixed(
              2
            )}m is outside valid range [0, ${enhancedDrillhole.depth}m]`
          );
          clickedDepth = Math.min(
            Math.max(0, clickedDepth),
            enhancedDrillhole.depth
          );
        }

        // Find which geology segment this depth falls into for debugging
        const matchingSegment = geologyData.find(
          (segment: any) =>
            clickedDepth! >= segment.from && clickedDepth! <= segment.to
        );

        console.log(
          `Clicked at depth: ${clickedDepth?.toFixed(2)}m, Total depth: ${
            enhancedDrillhole.depth
          }m`,
          matchingSegment
            ? `Segment: ${matchingSegment.from}-${matchingSegment.to}m`
            : "No matching segment"
        );
      }

      setContextMenu({
        visible: true,
        position: { x: clientX, y: clientY },
        clickedDepth,
      });
    },
    [enhancedDrillhole, calculateDepthFromPoint, geologyData]
  );

  const closeContextMenu = useCallback(() => {
    setContextMenu({
      ...contextMenu,
      visible: false,
    });
  }, [contextMenu]);

  // Handle click outside to close context menu
  useEffect(() => {
    const handleClickOutside = () => {
      if (contextMenu.visible) {
        closeContextMenu();
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [contextMenu, closeContextMenu]);

  // Create context menu container
  useEffect(() => {
    const id = "drillhole-context-menu-container";
    let container = document.getElementById(id);

    if (!container) {
      container = document.createElement("div");
      container.id = id;
      document.body.appendChild(container);
    }

    return () => {
      const existingContainer = document.getElementById(id);
      if (existingContainer) {
        document.body.removeChild(existingContainer);
      }
    };
  }, []);

  // Handle "Log this drill hole" option
  const handleLogDrillhole = useCallback(() => {
    // Dispatch the same actions as refreshImageData in logging.tsx
    dispatch(
      updateSelectedDrilhole({
        value: drillhole.id,
        label: drillhole.name,
      }) as any
    );

    dispatch(getDetailDrillhole(Number(drillhole.id)) as any);

    // Open logging page in a new tab
    window.open(`/logging?drillholeId=${drillhole.id}`, "_blank");
  }, [dispatch, drillhole]);

  // Handle "Show Log" option
  const handleShowLog = useCallback(() => {
    // Find the geology segment that was clicked based on the stored depth
    if (contextMenu.clickedDepth !== undefined && geologyData.length > 0) {
      const clickedSegment = geologyData.find(
        (segment: any) =>
          contextMenu.clickedDepth! >= segment.from &&
          contextMenu.clickedDepth! <= segment.to
      );

      if (clickedSegment && clickedSegment.dataEntry) {
        // Create a custom event with the geology data
        const event = new CustomEvent("showGeologyLog", {
          detail: {
            geologyData: clickedSegment,
            drillholeName: drillhole.name,
          },
        });

        // Dispatch the event for the parent app to handle
        document.dispatchEvent(event);
      }
    }
    closeContextMenu();
  }, [contextMenu.clickedDepth, geologyData, closeContextMenu, drillhole.name]);

  // Handle "Edit drill hole" option
  const handleEditDrillhole = useCallback(() => {
    // Create a custom event with the drillhole data
    console.log("drillhole: ", drillhole);

    const event = new CustomEvent("editDrillhole", {
      detail: drillhole,
    });

    // Dispatch the event for the parent app to handle
    document.dispatchEvent(event);
  }, [drillhole]);

  // Render context menu
  useEffect(() => {
    const containerId = "drillhole-context-menu-container";
    const container = document.getElementById(containerId);

    if (container && contextMenu.visible) {
      // Clear previous content
      container.innerHTML = "";

      // Create menu element
      const menuElement = document.createElement("div");
      menuElement.style.position = "fixed";
      menuElement.style.top = `${contextMenu.position.y}px`;
      menuElement.style.left = `${contextMenu.position.x}px`;
      menuElement.style.zIndex = "1000";
      menuElement.style.backgroundColor = "white";
      menuElement.style.border = "1px solid #ccc";
      menuElement.style.borderRadius = "4px";
      menuElement.style.boxShadow = "0 2px 10px rgba(0,0,0,0.1)";
      menuElement.style.padding = "4px 0";

      // Add drillhole name header
      const nameHeader = document.createElement("div");
      nameHeader.style.padding = "8px 16px";
      nameHeader.style.whiteSpace = "nowrap";
      nameHeader.style.fontWeight = "bold";
      nameHeader.style.fontSize = "14px";
      nameHeader.style.backgroundColor = "#f0f0f0";
      nameHeader.style.borderBottom = "1px solid #ddd";
      nameHeader.style.marginBottom = "4px";
      nameHeader.textContent = drillhole.name;
      menuElement.appendChild(nameHeader);

      // Add "Show Log" option (only if geology data is available)
      let showLogOption: HTMLDivElement | null = null;
      if (geologyData.length > 0 && contextMenu.clickedDepth !== undefined) {
        const clickedSegment = geologyData.find(
          (segment: any) =>
            contextMenu.clickedDepth! >= segment.from &&
            contextMenu.clickedDepth! <= segment.to
        );

        if (clickedSegment && clickedSegment.dataEntry) {
          showLogOption = document.createElement("div");
          showLogOption.style.padding = "8px 16px";
          showLogOption.style.cursor = "pointer";
          showLogOption.style.whiteSpace = "nowrap";
          showLogOption.textContent = "Show Log";

          showLogOption.addEventListener("mouseover", () => {
            showLogOption!.style.backgroundColor = "#f5f5f5";
          });
          showLogOption.addEventListener("mouseout", () => {
            showLogOption!.style.backgroundColor = "transparent";
          });
          showLogOption.addEventListener("click", () => {
            handleShowLog();
          });
        }
      }

      // Add "Edit Log" option
      const logOption = document.createElement("div");
      logOption.style.padding = "8px 16px";
      logOption.style.cursor = "pointer";
      logOption.style.whiteSpace = "nowrap";
      logOption.textContent = "Edit Log";

      logOption.addEventListener("mouseover", () => {
        logOption.style.backgroundColor = "#f5f5f5";
      });
      logOption.addEventListener("mouseout", () => {
        logOption.style.backgroundColor = "transparent";
      });
      logOption.addEventListener("click", () => {
        handleLogDrillhole();
        closeContextMenu();
      });

      // Add "Edit Drill Hole" option
      const editOption = document.createElement("div");
      editOption.style.padding = "8px 16px";
      editOption.style.cursor = "pointer";
      editOption.style.whiteSpace = "nowrap";
      editOption.textContent = "Edit Drill Hole";

      editOption.addEventListener("mouseover", () => {
        editOption.style.backgroundColor = "#f5f5f5";
      });
      editOption.addEventListener("mouseout", () => {
        editOption.style.backgroundColor = "transparent";
      });
      editOption.addEventListener("click", () => {
        handleEditDrillhole();
        closeContextMenu();
      });

      // Add options to menu
      if (showLogOption) {
        menuElement.appendChild(showLogOption);
      }
      menuElement.appendChild(logOption);
      menuElement.appendChild(editOption);
      container.appendChild(menuElement);
    } else if (container) {
      container.innerHTML = "";
    }
  }, [
    contextMenu,
    handleLogDrillhole,
    handleEditDrillhole,
    handleShowLog,
    closeContextMenu,
    geologyData,
  ]);

  return (
    <>
      <group onContextMenu={handleContextMenu}>
        {/* Base drillhole tube */}
        <mesh
          geometry={geometry}
          frustumCulled={false} // Ensure drillhole is always rendered regardless of distance
        >
          <meshStandardMaterial
            color={traceColor}
            transparent={true}
            opacity={1 - transparency}
            // Ensure visibility at distance
            depthTest={true}
            // Ensure material is always visible
            polygonOffset={true}
            polygonOffsetFactor={-1}
          />
        </mesh>

        {/* Geology segments if data is available */}
        {geologyData && geologyData.length > 0 && (
          <GeologyModel
            key={`geology-${drillhole.id}`}
            drillhole={enhancedDrillhole}
            geologyData={geologyData}
            config={config}
            transparency={transparency}
          />
        )}

        {/* Assay visualization if data is available */}
        {drillholeAssayData &&
          drillholeAssayData.length > 0 &&
          selectedAttribute && (
            <AssayModel
              key={`assay-${drillhole.id}`}
              drillhole={enhancedDrillhole}
              assayData={drillholeAssayData}
              selectedAttribute={selectedAttribute}
              config={config}
              transparency={transparency}
            />
          )}

        {/* Label for the drillhole */}
        {showLabel && (
          <Billboard
            position={getNormalizedPosition(enhancedDrillhole, 5)}
            follow={true}
            lockX={false}
            lockY={false}
            lockZ={false}
          >
            <Text
              fontSize={3}
              color={theme === "dark" ? "#ffffff" : "#000000"}
              anchorX="center"
              anchorY="middle"
              renderOrder={1} // Ensure text renders on top
            >
              {drillhole.name}
            </Text>
          </Billboard>
        )}
      </group>
    </>
  );
};

export default React.memo(DrillholeModel);
