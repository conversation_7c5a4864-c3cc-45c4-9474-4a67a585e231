"use client";
import {
  <PERSON><PERSON><PERSON>utlined,
  <PERSON>DownOutlined,
  <PERSON>UpOutlined,
  BgColorsOutlined,
  BoldOutlined,
  BorderOuterOutlined,
  ColumnHeightOutlined,
  EyeOutlined,
  FontColorsOutlined,
  FontSizeOutlined,
  NodeIndexOutlined,
  TagsOutlined,
} from "@ant-design/icons";
import {
  <PERSON>ton,
  Card,
  Collapse,
  ColorPicker,
  Divider,
  message,
  Slider,
  Space,
  Switch,
  Tooltip,
} from "antd";
import React, { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectDrillholeStyle,
  selectLayerLighting,
  selectLayerTransparency,
  selectLayerVisibility,
  selectRockTypeTextStyle,
  selectSectionConfig,
  selectSelectingRotationCenter,
  selectShowGrid,
  selectShowLabels,
  selectTheme,
} from "../redux/threedSlice/selectors";
import {
  resetRotationCenter,
  setLayerLighting,
  setLayerTransparency,
  toggleGrid,
  toggleLayerVisibility,
  toggleSection,
  toggleSelectingRotationCenter,
  toggleShowLabels,
  toggleTheme,
  updateDrillholeStyle,
  updateRockTypeTextStyle,
} from "../redux/threedSlice/threed.slice";

// Debounce utility function
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
) => {
  let timeoutId: NodeJS.Timeout;

  return function (...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

const ControlPanel: React.FC = () => {
  const dispatch = useDispatch();
  // State to track which panel is active
  const [activePanel, setActivePanel] = React.useState<string | string[]>([
    "camera",
    "layers",
  ]);

  // Layer-related states - pull these up to component level
  const isObjectLayerVisible = useSelector(
    selectLayerVisibility("objectLayer")
  );
  const objectLayerTransparency = useSelector(
    selectLayerTransparency("objectLayer")
  );
  const objectLayerLighting = useSelector(selectLayerLighting("objectLayer"));

  const isDrillholeLayerVisible = useSelector(
    selectLayerVisibility("drillholeLayer")
  );
  const drillholeLayerTransparency = useSelector(
    selectLayerTransparency("drillholeLayer")
  );
  const drillholeLayerLighting = useSelector(
    selectLayerLighting("drillholeLayer")
  );

  // Rock type text styling state
  const rockTypeTextStyle = useSelector(selectRockTypeTextStyle);

  // Get theme state at the top level
  const currentTheme = useSelector(selectTheme);

  // Get selection mode state from Redux
  let selectingRotationCenter = false;

  // Initialize with default values
  let sectionConfig: {
    enabled: boolean;
    direction: "ew" | "ns";
    position: number;
  } = {
    enabled: false,
    direction: "ew",
    position: 50,
  };
  let showLabels = true;
  let theme: "light" | "dark" = "light";
  let showGrid = true;
  let drillholeStyle = {
    traceColor: "#000000",
    traceWidth: 1.0,
    rockGroupWidth: 1.5,
  };

  // Try to get values from store, use defaults if store not initialized
  try {
    sectionConfig = useSelector(selectSectionConfig);
    showLabels = useSelector(selectShowLabels);
    theme = useSelector(selectTheme);
    showGrid = useSelector(selectShowGrid);
    drillholeStyle = useSelector(selectDrillholeStyle);
    selectingRotationCenter = useSelector(selectSelectingRotationCenter);
    // Get additional data needed for refresh functionality
  } catch (error) {
    console.log(
      "Error selecting from threed slice - might not be registered yet"
    );
  }

  // Camera presets handlers
  const handleTopView = () => {
    // Set camera to top view (looking down from above)
    // Position: [0, 0, 200] - High up on Z axis (elevation)
    // Target: [0, 0, 0] - Looking at the origin
    const cameraEvent = new CustomEvent("camera-control", {
      detail: {
        position: [0, 0, 200],
        target: [0, 0, 0],
        up: [0, 1, 0], // Y-up (north) for correct orientation in top view
      },
    });
    window.dispatchEvent(cameraEvent);
  };

  const handleEastView = () => {
    // Set camera to east view (looking from east to west)
    // Position: [200, 0, 0] - Far along positive X axis (east)
    // Target: [0, 0, 0] - Looking at the origin
    const cameraEvent = new CustomEvent("camera-control", {
      detail: {
        position: [200, 0, 0],
        target: [0, 0, 0],
        up: [0, 0, 1], // Z-up (elevation) for proper orientation
      },
    });
    window.dispatchEvent(cameraEvent);
  };

  const handleNorthView = () => {
    // Set camera to north view (looking from north to south)
    // Position: [0, 200, 0] - Far along positive Y axis (north)
    // Target: [0, 0, 0] - Looking at the origin
    const cameraEvent = new CustomEvent("camera-control", {
      detail: {
        position: [0, 200, 0],
        target: [0, 0, 0],
        up: [0, 0, 1], // Z-up (elevation) for proper orientation
      },
    });
    window.dispatchEvent(cameraEvent);
  };

  const handleIsoView = () => {
    // Set camera to isometric view (looking from diagonal angle)
    // Position: [220, 180, 50] - Equal distance on all axes for true isometric view
    // Target: [0, 0, 0] - Looking at the origin
    const cameraEvent = new CustomEvent("camera-control", {
      detail: {
        position: [220, 180, 50],
        target: [0, 0, 0],
        up: [0, 0, 1], // Z-up (elevation) for proper orientation
      },
    });
    window.dispatchEvent(cameraEvent);
  };

  // Toggle handlers
  const handleToggleLabels = (checked: boolean) => {
    // @ts-ignore - Bypassing type check temporarily until we resolve the Redux store typing
    dispatch(toggleShowLabels(checked));
  };

  // Section controls
  const handleToggleSection = (checked: boolean) => {
    // @ts-ignore - Bypassing type check temporarily until we resolve the Redux store typing
    dispatch(toggleSection({ enabled: checked }));
  };

  const handleSectionDirectionChange = (value: "ew" | "ns") => {
    // @ts-ignore - Bypassing type check temporarily until we resolve the Redux store typing
    dispatch(
      toggleSection({
        enabled: sectionConfig.enabled,
        direction: value,
      })
    );
  };

  const handleSectionPositionChange = (value: number) => {
    // @ts-ignore - Bypassing type check temporarily until we resolve the Redux store typing
    dispatch(
      toggleSection({
        enabled: sectionConfig.enabled,
        position: value,
      })
    );
  };

  // Handler for toggle rotation center selection mode
  const handleToggleRotationCenterMode = () => {
    dispatch(toggleSelectingRotationCenter());
  };

  // Handler for resetting rotation center
  const handleResetRotationCenter = () => {
    dispatch(resetRotationCenter());
    message.success("Reset center of rotation to origin (0,0,0)");
  };

  return (
    <Card
      title={
        <div className="flex items-center">
          <div className="flex items-center gap-2">
            <BorderOuterOutlined />
            <span>Control Panel</span>
          </div>
        </div>
      }
      className="shadow-md w-80 md:w-80 lg:w-80 bg-white rounded-lg max-h-[calc(100vh_-_156px)] overflow-auto select-none"
      bodyStyle={{ padding: "0px" }}
    >
      <Collapse
        activeKey={activePanel}
        onChange={(key) => setActivePanel(key)}
        bordered={false}
        className="bg-white"
        accordion={true}
      >
        {/* Camera Controls Panel */}
        <Collapse.Panel
          key="camera"
          header={
            <div className="flex items-center gap-2">
              <ArrowDownOutlined />
              <span className="font-medium text-sm">Camera Perspectives</span>
            </div>
          }
          className="bg-gray-50 rounded"
        >
          <div className="space-y-1">
            <div className="grid grid-cols-2 gap-3 p-1 rounded">
              <Tooltip title="Top View (Plan)">
                <Button
                  onClick={handleTopView}
                  icon={<ArrowDownOutlined />}
                  className="flex items-center justify-center h-9"
                >
                  <span className="ml-2 text-xs">Top</span>
                </Button>
              </Tooltip>
              <Tooltip title="East View (E-W Section)">
                <Button
                  onClick={handleEastView}
                  icon={<ArrowUpOutlined />}
                  className="flex items-center justify-center h-9"
                >
                  <span className="ml-2 text-xs">East</span>
                </Button>
              </Tooltip>
              <Tooltip title="North View (N-S Section)">
                <Button
                  onClick={handleNorthView}
                  icon={<BorderOuterOutlined />}
                  className="flex items-center justify-center h-9"
                >
                  <span className="ml-2 text-xs">North</span>
                </Button>
              </Tooltip>
              <Tooltip title="Isometric View (3D)">
                <Button
                  onClick={handleIsoView}
                  icon={<NodeIndexOutlined />}
                  className="flex items-center justify-center h-9"
                >
                  <span className="ml-2 text-xs">Reset</span>
                </Button>
              </Tooltip>
            </div>
            <Divider className="my-1" />
            <div className="p-1">
              <div className="grid grid-cols-2 gap-2">
                <Tooltip title="Click on a drillhole to set it as the center of rotation">
                  <Button
                    onClick={handleToggleRotationCenterMode}
                    icon={<AimOutlined />}
                    type={selectingRotationCenter ? "primary" : "default"}
                    className="flex items-center justify-center h-9"
                    danger={selectingRotationCenter}
                  >
                    <span className="ml-1 text-xs">
                      {selectingRotationCenter ? "Click" : "Set Center"}
                    </span>
                  </Button>
                </Tooltip>

                <Tooltip title="Reset center of rotation to origin (0,0,0)">
                  <Button
                    onClick={handleResetRotationCenter}
                    icon={<BorderOuterOutlined />}
                    className="flex items-center justify-center h-9"
                  >
                    <span className="ml-1 text-xs">Reset</span>
                  </Button>
                </Tooltip>
              </div>
            </div>
          </div>
        </Collapse.Panel>

        {/* Display Options Panel */}
        <Collapse.Panel
          key="display"
          header={
            <div className="flex items-center gap-2">
              <TagsOutlined />
              <span className="font-medium text-sm">Display Settings</span>
            </div>
          }
          className="bg-gray-50 rounded"
        >
          <div className="space-y-4 p-0">
            {/* Drillhole Trace Controls */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Trace Color:</span>
                <ColorPicker
                  size="small"
                  value={drillholeStyle.traceColor}
                  onChange={(color) =>
                    dispatch(
                      updateDrillholeStyle({ traceColor: color.toHexString() })
                    )
                  }
                />
              </div>
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">Trace Width:</span>
                  <span className="text-xs bg-gray-100 px-3 py-1 rounded-md font-medium">
                    {drillholeStyle.traceWidth}
                  </span>
                </div>
                <Slider
                  value={drillholeStyle.traceWidth}
                  onChange={(value) =>
                    dispatch(updateDrillholeStyle({ traceWidth: value }))
                  }
                  min={0.1}
                  max={5}
                  step={0.1}
                  tooltip={{
                    formatter: (value: number | undefined) =>
                      value !== undefined ? `${value}` : "",
                  }}
                />
              </div>
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">Rock Group Width:</span>
                  <span className="text-xs bg-gray-100 px-3 py-1 rounded-md font-medium">
                    {drillholeStyle.rockGroupWidth}
                  </span>
                </div>
                <Slider
                  value={drillholeStyle.rockGroupWidth}
                  onChange={(value) =>
                    dispatch(updateDrillholeStyle({ rockGroupWidth: value }))
                  }
                  min={0.1}
                  max={5}
                  step={0.1}
                  tooltip={{
                    formatter: (value: number | undefined) =>
                      value !== undefined ? `${value}` : "",
                  }}
                />
              </div>
            </div>
            <Divider className="my-2" />
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <NodeIndexOutlined />
                <span className="text-sm">Dark Theme</span>
              </div>
              <Switch
                size="small"
                checked={theme === "dark"}
                onChange={() => dispatch(toggleTheme())}
              />
            </div>
            <Divider className="my-2" />
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <BorderOuterOutlined />
                <span className="text-sm">Grid</span>
              </div>
              <Switch
                size="small"
                checked={showGrid}
                onChange={() => dispatch(toggleGrid())}
              />
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <TagsOutlined />
                <span className="text-sm">Labels</span>
              </div>
              <Switch
                size="small"
                checked={showLabels}
                onChange={handleToggleLabels}
              />
            </div>
          </div>
        </Collapse.Panel>

        {/* Rock Type Text Styling Panel */}
        <Collapse.Panel
          key="rockTypeText"
          header={
            <div className="flex items-center gap-2">
              <FontColorsOutlined />
              <span className="font-medium text-sm">Geology Code Text</span>
            </div>
          }
          className="bg-gray-50 rounded"
        >
          <div className="space-y-4 p-0">
            {/* Rock Type Text Controls */}
            <div className="space-y-3">
              {/* Font Size Control */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <div className="flex items-center gap-2">
                    <FontSizeOutlined />
                    <span className="text-sm font-medium">Font Size:</span>
                  </div>
                  <span className="text-xs bg-gray-100 px-3 py-1 rounded-md font-medium">
                    {rockTypeTextStyle.fontSize}
                  </span>
                </div>
                <Slider
                  value={rockTypeTextStyle.fontSize}
                  onChange={useCallback(
                    debounce(
                      (value) =>
                        dispatch(updateRockTypeTextStyle({ fontSize: value })),
                      100
                    ),
                    [dispatch]
                  )}
                  min={0.5}
                  max={10}
                  step={0.1}
                  tooltip={{
                    formatter: (value: number | undefined) =>
                      value !== undefined ? `${value}` : "",
                  }}
                />
              </div>

              {/* Text Color Control */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <BgColorsOutlined />
                  <span className="text-sm font-medium">Text Color:</span>
                </div>
                <ColorPicker
                  size="small"
                  value={
                    rockTypeTextStyle.color ||
                    (currentTheme === "dark" ? "#FFFFFF" : "#000000")
                  }
                  onChange={useCallback(
                    debounce(
                      (color) =>
                        dispatch(
                          updateRockTypeTextStyle({
                            color: color.toHexString(),
                          })
                        ),
                      100
                    ),
                    [dispatch]
                  )}
                />
              </div>

              {/* Font Weight Control */}
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <BoldOutlined />
                  <span className="text-sm">Bold Text</span>
                </div>
                <Switch
                  size="small"
                  checked={rockTypeTextStyle.fontWeight === "bold"}
                  onChange={(checked) =>
                    dispatch(
                      updateRockTypeTextStyle({
                        fontWeight: checked ? "bold" : "normal",
                      })
                    )
                  }
                />
              </div>

              {/* Text Visibility Control */}
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <EyeOutlined />
                  <span className="text-sm">Show Text</span>
                </div>
                <Switch
                  size="small"
                  checked={rockTypeTextStyle.visible}
                  onChange={(checked) =>
                    dispatch(updateRockTypeTextStyle({ visible: checked }))
                  }
                />
              </div>
            </div>
          </div>
        </Collapse.Panel>

        {/* Section View Panel */}
        <Collapse.Panel
          key="section"
          header={
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <ColumnHeightOutlined />
                <span className="font-medium text-sm">Section View</span>
              </div>
              <Switch
                size="small"
                checked={sectionConfig.enabled}
                onChange={handleToggleSection}
                className="ml-auto"
              />
            </div>
          }
          className="bg-gray-50 rounded"
        >
          <div
            className={`space-y-4 p-0 ${
              !sectionConfig.enabled && "opacity-50 pointer-events-none"
            }`}
          >
            <div className="flex flex-col gap-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Direction:</span>
                <Space>
                  <Button
                    type={
                      sectionConfig.direction === "ew" ? "primary" : "default"
                    }
                    size="small"
                    onClick={() => handleSectionDirectionChange("ew")}
                    className="min-w-[48px]"
                  >
                    E-W
                  </Button>
                  <Button
                    type={
                      sectionConfig.direction === "ns" ? "primary" : "default"
                    }
                    size="small"
                    onClick={() => handleSectionDirectionChange("ns")}
                    className="min-w-[48px]"
                  >
                    N-S
                  </Button>
                </Space>
              </div>

              <div className="pt-2">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">Position:</span>
                  <span className="text-xs bg-gray-100 px-3 py-1 rounded-md font-medium">
                    {sectionConfig.position}%
                  </span>
                </div>
                <Slider
                  value={sectionConfig.position}
                  onChange={handleSectionPositionChange}
                  min={0}
                  max={100}
                  step={1}
                  className="mt-2"
                  tooltip={{
                    formatter: (value: number | undefined) =>
                      value !== undefined ? `${value}%` : "",
                  }}
                />
              </div>
            </div>
          </div>
        </Collapse.Panel>

        {/* Layer Controls Panel */}
        <Collapse.Panel
          key="layers"
          header={
            <div className="flex items-center gap-2">
              <NodeIndexOutlined />
              <span className="font-medium text-sm">Layer Controls</span>
            </div>
          }
          className="bg-gray-50 rounded"
        >
          <div className="space-y-2 p-0">
            {/* Drillhole Layer Controls */}
            <div className="space-y-3 border p-3 rounded-md bg-gray-50">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Drillhole Layer</span>
                <Switch
                  size="small"
                  checked={isDrillholeLayerVisible}
                  onChange={(checked) =>
                    dispatch(
                      toggleLayerVisibility({
                        layerName: "drillholeLayer",
                        visible: checked,
                      })
                    )
                  }
                />
              </div>

              {isDrillholeLayerVisible && (
                <>
                  {/* Drillhole Layer Transparency */}
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium">Transparency:</span>
                      <span className="text-xs bg-gray-100 px-3 py-1 rounded-md font-medium ml-2">
                        {Math.round(drillholeLayerTransparency * 100)}%
                      </span>
                    </div>
                    <Slider
                      value={drillholeLayerTransparency * 100}
                      onChange={(value) =>
                        dispatch(
                          setLayerTransparency({
                            layerName: "drillholeLayer",
                            transparency: value / 100,
                          })
                        )
                      }
                      min={0}
                      max={100}
                      step={1}
                      tooltip={{
                        formatter: (value: number | undefined) =>
                          value !== undefined ? `${value}%` : "",
                      }}
                      className="mt-2"
                    />
                  </div>

                  {/* Drillhole Layer Lighting */}
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium">
                        Ambient Light:
                      </span>
                      <div className="flex items-center gap-3">
                        <ColorPicker
                          size="small"
                          value={drillholeLayerLighting.ambient.color}
                          onChange={(color) =>
                            dispatch(
                              setLayerLighting({
                                layerName: "drillholeLayer",
                                lighting: {
                                  ambient: {
                                    color: color.toHexString(),
                                  },
                                },
                              })
                            )
                          }
                        />
                        <Slider
                          value={drillholeLayerLighting.ambient.intensity * 10}
                          onChange={(value) =>
                            dispatch(
                              setLayerLighting({
                                layerName: "drillholeLayer",
                                lighting: {
                                  ambient: {
                                    intensity: value / 10,
                                  },
                                },
                              })
                            )
                          }
                          min={0}
                          max={20}
                          step={0.1}
                          className="w-24"
                          tooltip={{
                            formatter: (value: number | undefined) =>
                              value !== undefined
                                ? `${(value / 10).toFixed(1)}`
                                : "",
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium">
                        Directional Light:
                      </span>
                      <div className="flex items-center gap-3">
                        <ColorPicker
                          size="small"
                          value={drillholeLayerLighting.directional.color}
                          onChange={(color) =>
                            dispatch(
                              setLayerLighting({
                                layerName: "drillholeLayer",
                                lighting: {
                                  directional: {
                                    color: color.toHexString(),
                                  },
                                },
                              })
                            )
                          }
                        />
                        <Slider
                          value={
                            drillholeLayerLighting.directional.intensity * 10
                          }
                          onChange={(value) =>
                            dispatch(
                              setLayerLighting({
                                layerName: "drillholeLayer",
                                lighting: {
                                  directional: {
                                    intensity: value / 10,
                                  },
                                },
                              })
                            )
                          }
                          min={0}
                          max={20}
                          step={0.1}
                          className="w-24"
                          tooltip={{
                            formatter: (value: number | undefined) =>
                              value !== undefined
                                ? `${(value / 10).toFixed(1)}`
                                : "",
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>

            <Divider className="my-0" />

            {/* Object Layer Controls */}
            <div className="space-y-3 border p-3 rounded-md bg-gray-50">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Object Layer</span>
                <Switch
                  size="small"
                  checked={isObjectLayerVisible}
                  onChange={(checked) =>
                    dispatch(
                      toggleLayerVisibility({
                        layerName: "objectLayer",
                        visible: checked,
                      })
                    )
                  }
                />
              </div>

              {isObjectLayerVisible && (
                <>
                  {/* Object Layer Transparency */}
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium">Transparency:</span>
                      <span className="text-xs bg-gray-100 px-3 py-1 rounded-md font-medium ml-2">
                        {Math.round(objectLayerTransparency * 100)}%
                      </span>
                    </div>
                    <Slider
                      value={objectLayerTransparency * 100}
                      onChange={(value) =>
                        dispatch(
                          setLayerTransparency({
                            layerName: "objectLayer",
                            transparency: value / 100,
                          })
                        )
                      }
                      min={0}
                      max={100}
                      step={1}
                      tooltip={{
                        formatter: (value: number | undefined) =>
                          value !== undefined ? `${value}%` : "",
                      }}
                      className="mt-2"
                    />
                  </div>

                  {/* Object Layer Lighting */}
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium">
                        Ambient Light:
                      </span>
                      <div className="flex items-center gap-3">
                        <ColorPicker
                          size="small"
                          value={objectLayerLighting.ambient.color}
                          onChange={(color) =>
                            dispatch(
                              setLayerLighting({
                                layerName: "objectLayer",
                                lighting: {
                                  ambient: {
                                    color: color.toHexString(),
                                  },
                                },
                              })
                            )
                          }
                        />
                        <Slider
                          value={objectLayerLighting.ambient.intensity * 10}
                          onChange={(value) =>
                            dispatch(
                              setLayerLighting({
                                layerName: "objectLayer",
                                lighting: {
                                  ambient: {
                                    intensity: value / 10,
                                  },
                                },
                              })
                            )
                          }
                          min={0}
                          max={20}
                          step={0.1}
                          className="w-24"
                          tooltip={{
                            formatter: (value: number | undefined) =>
                              value !== undefined
                                ? `${(value / 10).toFixed(1)}`
                                : "",
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium">
                        Directional Light:
                      </span>
                      <div className="flex items-center gap-3">
                        <ColorPicker
                          size="small"
                          value={objectLayerLighting.directional.color}
                          onChange={(color) =>
                            dispatch(
                              setLayerLighting({
                                layerName: "objectLayer",
                                lighting: {
                                  directional: {
                                    color: color.toHexString(),
                                  },
                                },
                              })
                            )
                          }
                        />
                        <Slider
                          value={objectLayerLighting.directional.intensity * 10}
                          onChange={(value) =>
                            dispatch(
                              setLayerLighting({
                                layerName: "objectLayer",
                                lighting: {
                                  directional: {
                                    intensity: value / 10,
                                  },
                                },
                              })
                            )
                          }
                          min={0}
                          max={20}
                          step={0.1}
                          className="w-24"
                          tooltip={{
                            formatter: (value: number | undefined) =>
                              value !== undefined
                                ? `${(value / 10).toFixed(1)}`
                                : "",
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </Collapse.Panel>
      </Collapse>
    </Card>
  );
};

export default ControlPanel;
