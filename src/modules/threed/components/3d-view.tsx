"use client";
import React, { useEffect } from "react";

// Import Redux actions and selectors
import { getAllDesurveyResultByDrillHole } from "../redux/threedSlice/thunks";

// Import styles
import "../styles/3d-view.css";

// Import the extracted components
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import {
  setAssayAttributeId,
  setAssaySuiteId,
  setGeologySuiteId,
} from "../redux/threedSlice/threed.slice";
import Canvas3DViewWithModal from "./canvas-3d-view-with-modal";
import ControlOverlay from "./control-overlay";
import DrillholeListOverlay from "./drillhole-list-overlay";
import SelectionMessage from "./selection-message";

interface Logging3DViewProps {
  showControls: boolean;
  setShowControls: (value: boolean) => void;
  showDrillholeList: boolean;
  setShowDrillholeList: (value: boolean) => void;
  showGeologySuite: boolean;
  setShowGeologySuite: (value: boolean) => void;
  showAssayPanel: boolean;
  setShowAssayPanel: (value: boolean) => void;
  modelUrl?: string | null;
  modelCoordinates?: { east: number; north: number; rl: number };
  useFileCoordinates?: boolean;
}

export const Logging3DView: React.FC<Logging3DViewProps> = ({
  showControls,
  showDrillholeList,
  showGeologySuite,
  showAssayPanel,
  modelUrl,
  modelCoordinates,
  useFileCoordinates = true,
}) => {
  const dispatch = useAppDispatch();

  // Get global project ID and prospect ID from Redux store if available
  const globalProjectId = useAppSelector(
    (state: any) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state: any) => state.user?.userInfo?.prospectId
  );

  // Fetch drill holes when component mounts or when project/prospect changes
  useEffect(() => {
    if (globalProjectId) {
      // @ts-ignore - Using unknown action type until proper Redux typing is configured
      dispatch(
        getAllDesurveyResultByDrillHole({
          skipCount: 0,
          maxResultCount: 1000,
          projectId: globalProjectId,
        }) as any
      );
    }
  }, [dispatch, globalProjectId, globalProspectId]);

  useEffect(() => {
    dispatch(setGeologySuiteId(null));
    dispatch(setAssayAttributeId(null));
    dispatch(setAssaySuiteId(null));
  }, [globalProjectId, globalProspectId]);

  return (
    <div className="relative h-full w-full">
      {/* Render UI components conditionally based on showControls and showDrillholeList */}
      <ControlOverlay showControls={showControls} />
      <DrillholeListOverlay
        showDrillholeList={showDrillholeList}
        showGeologySuite={showGeologySuite}
        showAssayPanel={showAssayPanel}
      />
      <SelectionMessage />

      {/* Memoized 3D Canvas Component - prevents re-rendering on panel visibility changes */}
      <div className="threejs-canvas-container w-full h-full">
        <Canvas3DViewWithModal
          modelUrl={modelUrl}
          modelCoordinates={modelCoordinates}
          useFileCoordinates={useFileCoordinates}
        />
      </div>
    </div>
  );
};
