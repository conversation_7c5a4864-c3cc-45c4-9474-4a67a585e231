import { RequestState } from "@/common/configs/app.contants";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  DataEntry,
  DrillholeData,
  DrillholeDisplayMode,
  GeologyData,
  GeologySegment,
} from "../../interface/threed.interface";
import {
  getAllDesurveyResultByDrillHole,
  getDrillholes,
  getGeologyData,
} from "./thunks";

interface LayerSettings {
  visible: boolean;
  transparency: number; // 0-1 where 0 is opaque and 1 is fully transparent
  lighting: {
    ambient: {
      color: string;
      intensity: number;
    };
    directional: {
      color: string;
      intensity: number;
      position: [number, number, number];
    };
  };
}

interface ThreedState {
  theme: "light" | "dark";
  showGrid: boolean;
  drillholeStyle: {
    traceColor: string;
    traceWidth: number;
    rockGroupWidth: number;
  };
  rockTypeTextStyle: {
    fontSize: number;
    color: string;
    fontWeight: string;
    visible: boolean;
  };
  // Flag to track if DrillholeDisplayPanel data has been loaded
  drillholeDisplayPanelLoaded: boolean;
  // Flag to track if AssayPanel data has been loaded
  assayPanelLoaded: boolean;
  // Display mode for drillhole visualization
  drillholeDisplayMode: DrillholeDisplayMode;
  drillholes: {
    data: DrillholeData[];
    status: RequestState;
    error?: string;
  };
  geologyData: {
    [drillholeId: string]: {
      data: GeologySegment[];
      status: RequestState;
      error?: string;
    };
  };
  selectedDrillholes: string[];
  visibilityMap: Record<string, boolean>;
  camera: {
    position: [number, number, number];
    target: [number, number, number];
  };
  showLabels: boolean;
  showLegend: boolean;
  sectionConfig: {
    enabled: boolean;
    direction: "ew" | "ns";
    position: number; // 0-100%
  };
  coordinateBase: {
    east: number;
    north: number;
    rl: number;
  };
  // Selection mode for setting center of rotation
  selectingRotationCenter: boolean;
  // Custom rotation center marker visibility
  showRotationCenterMarker: boolean;
  // Show notification message about selection mode
  showSelectionMessage: boolean;
  // Geology suite and rock group selection
  geologySuiteId: number | null;
  rockGroupId: number | null;
  geologySuites: any[];
  rockGroups: any[];
  // Assay suite and attribute selection
  assaySuiteId: number | null;
  assayAttributeId: number | null;
  assaySuites: any[];
  assayAttributes: any[];
  assayData: any;
  // Dual layer system
  layers: {
    objectLayer: LayerSettings;
    drillholeLayer: LayerSettings;
  };
}

// Import defaults from helper
import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { DEFAULT_COORDINATE_BASE } from "../../helpers/drillhole.helper";

const initialState: ThreedState = {
  theme: "dark",
  showGrid: true,
  drillholeStyle: {
    traceColor: "#000000", // Black default for light theme
    traceWidth: 1.0,
    rockGroupWidth: 4,
  },
  rockTypeTextStyle: {
    fontSize: 1.2,
    color: "", // Will respect the theme by default
    fontWeight: "normal",
    visible: true,
  },
  drillholeDisplayPanelLoaded: false,
  assayPanelLoaded: false,
  drillholeDisplayMode: "drillhole", // Default to drillhole mode
  drillholes: {
    data: [],
    status: RequestState.idle,
  },
  geologyData: {},
  selectedDrillholes: [],
  visibilityMap: {},
  camera: {
    position: [220, 180, 50],
    target: [0, 0, 0],
  },
  showLabels: true,
  showLegend: true,
  sectionConfig: {
    enabled: false,
    direction: "ew",
    position: 50,
  },
  // Initialize with default values from helper instead of zeros
  coordinateBase: { ...DEFAULT_COORDINATE_BASE },
  // Selection mode for setting center of rotation
  selectingRotationCenter: false,
  // Marker visibility flag
  showRotationCenterMarker: true,
  // Selection notification message visibility
  showSelectionMessage: false,
  // Geology suite and rock group selection
  geologySuiteId: null,
  rockGroupId: null,
  geologySuites: [],
  rockGroups: [],
  // Assay suite and attribute selection
  assaySuiteId: null,
  assayAttributeId: null,
  assaySuites: [],
  assayAttributes: [],
  assayData: null,
  // Initialize dual layer system
  layers: {
    objectLayer: {
      visible: true,
      transparency: 0,
      lighting: {
        ambient: {
          color: "#ffffff",
          intensity: 1.0,
        },
        directional: {
          color: "#ffffff",
          intensity: 1.5,
          position: [1, 1, 1],
        },
      },
    },
    drillholeLayer: {
      visible: true,
      transparency: 0,
      lighting: {
        ambient: {
          color: "#ffffff",
          intensity: 1.0,
        },
        directional: {
          color: "#ffffff",
          intensity: 1.5,
          position: [1, 1, 1],
        },
      },
    },
  },
};

const threedSlice = createSlice({
  name: "threeD",
  initialState,
  reducers: {
    toggleTheme(state) {
      state.theme = state.theme === "light" ? "dark" : "light";
      // Update trace color based on theme
      state.drillholeStyle.traceColor =
        state.theme === "dark" ? "#FFFFFF" : "#000000";
    },
    updateDrillholeStyle(
      state,
      action: PayloadAction<{
        traceColor?: string;
        traceWidth?: number;
        rockGroupWidth?: number;
      }>
    ) {
      state.drillholeStyle = {
        ...state.drillholeStyle,
        ...action.payload,
      };
    },
    toggleDrillholeVisibility(
      state,
      action: PayloadAction<{ id: string; visible: boolean }>
    ) {
      const { id, visible } = action.payload;
      state.visibilityMap[id] = visible;
    },
    updateCameraPosition(
      state,
      action: PayloadAction<{
        position: [number, number, number];
        target: [number, number, number];
      }>
    ) {
      state.camera = action.payload;
    },
    toggleSection(
      state,
      action: PayloadAction<{
        enabled: boolean;
        direction?: "ew" | "ns";
        position?: number;
      }>
    ) {
      const { enabled, direction, position } = action.payload;
      state.sectionConfig.enabled = enabled;
      if (direction) state.sectionConfig.direction = direction;
      if (position !== undefined) state.sectionConfig.position = position;
    },
    selectDrillhole(state, action: PayloadAction<string>) {
      if (!state.selectedDrillholes.includes(action.payload)) {
        state.selectedDrillholes.push(action.payload);
      }
    },
    deselectDrillhole(state, action: PayloadAction<string>) {
      state.selectedDrillholes = state.selectedDrillholes.filter(
        (id) => id !== action.payload
      );
    },
    toggleShowLabels(state, action: PayloadAction<boolean>) {
      state.showLabels = action.payload;
    },
    toggleShowLegend(state, action: PayloadAction<boolean>) {
      state.showLegend = action.payload;
    },
    toggleGrid(state) {
      state.showGrid = !state.showGrid;
    },
    // Toggle selection mode for setting center of rotation
    toggleSelectingRotationCenter(state) {
      state.selectingRotationCenter = !state.selectingRotationCenter;
      // Show selection message when entering selection mode
      state.showSelectionMessage = state.selectingRotationCenter;
    },
    // Set new center of rotation
    setRotationCenter(state, action: PayloadAction<[number, number, number]>) {
      // Update camera target with new center point
      state.camera.target = action.payload;
      // Exit selection mode after setting center
      state.selectingRotationCenter = false;
      // Hide selection message
      state.showSelectionMessage = false;
    },
    // Reset center of rotation to default (0,0,0)
    resetRotationCenter(state) {
      // Reset camera target to origin
      state.camera.target = [0, 0, 0];
    },
    // Toggle visibility of rotation center marker
    toggleRotationCenterMarker(state, action: PayloadAction<boolean>) {
      state.showRotationCenterMarker = action.payload;
    },
    // Hide the selection message
    hideSelectionMessage(state) {
      state.showSelectionMessage = false;
    },
    // Set geology suite ID
    setGeologySuiteId(state, action: PayloadAction<number | null>) {
      state.geologySuiteId = action.payload;
      // Reset rock group when changing suite
      state.rockGroupId = null;
      // Mark panel as loaded when a suite is selected
      state.drillholeDisplayPanelLoaded = true;
    },
    // Set rock group ID
    setRockGroupId(state, action: PayloadAction<number | null>) {
      state.rockGroupId = action.payload;
    },
    // Set geology suites list
    setGeologySuites(state, action: PayloadAction<any[]>) {
      state.geologySuites = action.payload;
      // Set the panel as loaded if we have suites
      if (action.payload.length > 0) {
        state.drillholeDisplayPanelLoaded = true;
      }
    },
    // Set rock groups list
    setRockGroups(state, action: PayloadAction<any[]>) {
      state.rockGroups = action.payload;
    },
    // Set assay suite ID
    setAssaySuiteId(state, action: PayloadAction<number | null>) {
      state.assaySuiteId = action.payload;
      // Reset assay attribute when changing suite
      state.assayAttributeId = null;
      // Mark panel as loaded when a suite is selected
      state.assayPanelLoaded = true;
    },
    // Set assay attribute ID
    setAssayAttributeId(state, action: PayloadAction<number | null>) {
      state.assayAttributeId = action.payload;
    },
    // Set assay suites list
    setAssaySuites(state, action: PayloadAction<any[]>) {
      state.assaySuites = action.payload;
      // Set the panel as loaded if we have suites
      if (action.payload.length > 0) {
        state.assayPanelLoaded = true;
      }
    },
    // Set assay attributes list
    setAssayAttributes(state, action: PayloadAction<any[]>) {
      state.assayAttributes = action.payload;
    },
    // Set assay data
    setAssayData(state, action: PayloadAction<any>) {
      state.assayData = action.payload;
    },
    // Dual layer system actions
    toggleLayerVisibility(
      state,
      action: PayloadAction<{
        layerName: "objectLayer" | "drillholeLayer";
        visible: boolean;
      }>
    ) {
      const { layerName, visible } = action.payload;
      state.layers[layerName].visible = visible;
    },
    setLayerTransparency(
      state,
      action: PayloadAction<{
        layerName: "objectLayer" | "drillholeLayer";
        transparency: number;
      }>
    ) {
      const { layerName, transparency } = action.payload;
      // Ensure transparency is between 0 and 1
      state.layers[layerName].transparency = Math.max(
        0,
        Math.min(1, transparency)
      );
    },
    setLayerLighting(
      state,
      action: PayloadAction<{
        layerName: "objectLayer" | "drillholeLayer";
        lighting: {
          ambient?: {
            color?: string;
            intensity?: number;
          };
          directional?: {
            color?: string;
            intensity?: number;
            position?: [number, number, number];
          };
        };
      }>
    ) {
      const { layerName, lighting } = action.payload;

      if (lighting.ambient) {
        state.layers[layerName].lighting.ambient = {
          ...state.layers[layerName].lighting.ambient,
          ...lighting.ambient,
        };
      }

      if (lighting.directional) {
        state.layers[layerName].lighting.directional = {
          ...state.layers[layerName].lighting.directional,
          ...lighting.directional,
        };
      }
    },
    updateRockTypeTextStyle(
      state,
      action: PayloadAction<{
        fontSize?: number;
        color?: string;
        fontWeight?: string;
        visible?: boolean;
      }>
    ) {
      state.rockTypeTextStyle = {
        ...state.rockTypeTextStyle,
        ...action.payload,
      };
    },
    setDrillholeDisplayMode(
      state,
      action: PayloadAction<DrillholeDisplayMode>
    ) {
      state.drillholeDisplayMode = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle getDrillholes states
      .addCase(getDrillholes.pending, (state) => {
        state.drillholes.status = RequestState.pending;
      })
      .addCase(getDrillholes.fulfilled, (state, action) => {
        if (action.payload.state === RequestState.success) {
          state.drillholes.status = RequestState.success;
          state.drillholes.data = action.payload.data || [];

          // Skip coordinate base recalculation if skipCoordinateRecalculation flag is true
          const skipRecalculation =
            action.meta?.arg?.skipCoordinateRecalculation === true;

          // Calculate the coordinate base dynamically from the drillhole data only if not skipping
          if (state.drillholes.data.length > 0 && !skipRecalculation) {
            // Find minimum values for east, north, and rl
            const minEast = Math.min(
              ...state.drillholes.data.map((d) => d.east)
            );
            const minNorth = Math.min(
              ...state.drillholes.data.map((d) => d.north)
            );
            const minRL = Math.min(...state.drillholes.data.map((d) => d.rl));

            // Update coordinate base - ensure we don't use 0 as a default
            state.coordinateBase = {
              east: isFinite(minEast) ? minEast : state.coordinateBase.east,
              north: isFinite(minNorth) ? minNorth : state.coordinateBase.north,
              rl: isFinite(minRL) ? minRL : state.coordinateBase.rl,
            };
          }
        } else {
          state.drillholes.status = RequestState.error;
          state.drillholes.error = action.payload.message;
        }
      })
      .addCase(getDrillholes.rejected, (state, action) => {
        state.drillholes.status = RequestState.error;
        state.drillholes.error = action.error.message;
      })

      // Handle getDrillholesFromDesurvey states (replaces getDrillholes)
      .addCase(getAllDesurveyResultByDrillHole.pending, (state) => {
        state.drillholes.status = RequestState.pending;
      })
      .addCase(getAllDesurveyResultByDrillHole.fulfilled, (state, action) => {
        console.log("action.payload: ", action.payload);

        if (action.payload.state === RequestState.success) {
          state.drillholes.status = RequestState.success;
          state.drillholes.data = action.payload.data || [];

          console.log("Transformed drillhole data: ", state.drillholes.data);

          // Calculate the coordinate base dynamically from the drillhole data
          if (state.drillholes.data.length > 0) {
            // Filter out drillholes with invalid coordinates
            const validDrillholes = state.drillholes.data.filter(
              (d) =>
                d &&
                typeof d.east === "number" &&
                typeof d.north === "number" &&
                typeof d.rl === "number"
            );

            if (validDrillholes.length > 0) {
              // Find minimum values for east, north, and rl
              const minEast = Math.min(...validDrillholes.map((d) => d.east));
              const minNorth = Math.min(...validDrillholes.map((d) => d.north));
              const minRL = Math.min(...validDrillholes.map((d) => d.rl));

              // For negative coordinates, we want to use the minimum as base
              // This ensures negative values are drawn in the opposite direction of 0
              // We use minimum values to maintain proper coordinate relationships
              state.coordinateBase = {
                east: isFinite(minEast) ? minEast : state.coordinateBase.east,
                north: isFinite(minNorth)
                  ? minNorth
                  : state.coordinateBase.north,
                rl: isFinite(minRL) ? minRL : state.coordinateBase.rl,
              };

              console.log("Updated coordinate base:", state.coordinateBase);
              console.log("Coordinate range:", {
                east: {
                  min: minEast,
                  max: Math.max(...validDrillholes.map((d) => d.east)),
                },
                north: {
                  min: minNorth,
                  max: Math.max(...validDrillholes.map((d) => d.north)),
                },
                rl: {
                  min: minRL,
                  max: Math.max(...validDrillholes.map((d) => d.rl)),
                },
              });
            } else {
              console.warn(
                "No valid drillholes found for coordinate base calculation"
              );
              // Keep the existing coordinate base
            }
          }
        } else {
          state.drillholes.status = RequestState.error;
          state.drillholes.error = action.payload.message;
        }
      })
      .addCase(getAllDesurveyResultByDrillHole.rejected, (state, action) => {
        state.drillholes.status = RequestState.error;
        state.drillholes.error = action.error.message;
      })

      // Handle getGeologyData states with new parameter structure
      .addCase(getGeologyData.pending, (state, action) => {
        // Parse drillholeIds to get the first one to update in the state
        try {
          const drillholeIds = JSON.parse(action.meta.arg.drillholeIds);
          const targetDrillholeId = String(drillholeIds[0]); // Get the first drillhole ID as string

          state.geologyData[targetDrillholeId] = {
            data: [],
            status: RequestState.pending,
          };
        } catch (error) {}
      })
      .addCase(getGeologyData.fulfilled, (state, action) => {
        try {
          // Parse drillholeIds to get the first one to update in the state
          const drillholeIds = JSON.parse(action.meta.arg.drillholeIds);

          if (action.payload.state === RequestState.success) {
            drillholeIds.forEach((id: number) => {
              const foundDrillhole = (action.payload.data || []).find(
                (item: GeologyData) => item.drillholeId === id
              );

              const geologyDrillholeData = (
                foundDrillhole?.dataEntry || []
              ).map((dataEntry: DataEntry) => {
                console.log("dataEntry: ", dataEntry);
                const foundEntry = dataEntry.dataEntryValues.find(
                  (dataEntryValue) => {
                    return dataEntryValue.fieldType == FieldType.RockGroup;
                  }
                );

                return {
                  from: dataEntry.depthFrom,
                  to: dataEntry.depthTo,
                  rockType: foundEntry?.rockType?.code,
                  color: foundEntry?.rockType?.rockStyle.fillColor,
                  drillholeId: dataEntry.drillholeId,
                  dataEntry: dataEntry?.dataEntryValues,
                };
              });

              state.geologyData[id] = {
                data: geologyDrillholeData,
                status: RequestState.success,
              };
            });
          }
        } catch (error) {
          console.error(
            "Error parsing drillholeIds in fulfilled action:",
            error
          );
        }
      })
      .addCase(getGeologyData.rejected, (state, action) => {
        try {
          // Parse drillholeIds to get the first one to update in the state
          const drillholeIds = JSON.parse(action.meta.arg.drillholeIds);
          const targetDrillholeId = String(drillholeIds[0]); // Get the first drillhole ID as string

          state.geologyData[targetDrillholeId] = {
            data: [],
            status: RequestState.error,
            error: action.error.message,
          };
        } catch (error) {
          console.error(
            "Error parsing drillholeIds in rejected action:",
            error
          );
        }
      });
  },
});

export const {
  toggleDrillholeVisibility,
  updateCameraPosition,
  toggleSection,
  selectDrillhole,
  deselectDrillhole,
  toggleShowLabels,
  toggleShowLegend,
  toggleTheme,
  toggleGrid,
  updateDrillholeStyle,
  updateRockTypeTextStyle,
  toggleSelectingRotationCenter,
  setRotationCenter,
  resetRotationCenter,
  toggleRotationCenterMarker,
  hideSelectionMessage,
  setGeologySuiteId,
  setRockGroupId,
  setGeologySuites,
  setRockGroups,
  setAssaySuiteId,
  setAssayAttributeId,
  setAssaySuites,
  setAssayAttributes,
  setAssayData,
  toggleLayerVisibility,
  setLayerTransparency,
  setLayerLighting,
  setDrillholeDisplayMode,
} = threedSlice.actions;

// Selectors
export const selectCoordinateBase = (state: { threeD: ThreedState }) =>
  state.threeD.coordinateBase;

export const selectTheme = (state: { threeD: ThreedState }) =>
  state.threeD.theme;
export const selectShowGrid = (state: { threeD: ThreedState }) =>
  state.threeD.showGrid;

export const selectDrillholeStyle = (state: { threeD: ThreedState }) =>
  state.threeD.drillholeStyle;

// New selectors for rotation center functionality
export const selectSelectingRotationCenter = (state: { threeD: ThreedState }) =>
  state.threeD.selectingRotationCenter;

export const selectCameraTarget = (state: { threeD: ThreedState }) =>
  state.threeD.camera.target;

export const selectShowRotationCenterMarker = (state: {
  threeD: ThreedState;
}) => state.threeD.showRotationCenterMarker;

export const selectShowSelectionMessage = (state: { threeD: ThreedState }) =>
  state.threeD.showSelectionMessage;

// Geology suite and rock group selectors
export const selectGeologySuiteId = (state: { threeD: ThreedState }) =>
  state.threeD.geologySuiteId;

export const selectRockGroupId = (state: { threeD: ThreedState }) =>
  state.threeD.rockGroupId;

export const selectGeologySuites = (state: { threeD: ThreedState }) =>
  state.threeD.geologySuites;

export const selectRockGroups = (state: { threeD: ThreedState }) =>
  state.threeD.rockGroups;

export const selectDrillholeDisplayPanelLoaded = (state: {
  threeD: ThreedState;
}) => state.threeD.drillholeDisplayPanelLoaded;

// Assay panel selectors
export const selectAssaySuiteId = (state: { threeD: ThreedState }) =>
  state.threeD.assaySuiteId;

export const selectAssayAttributeId = (state: { threeD: ThreedState }) =>
  state.threeD.assayAttributeId;

export const selectAssaySuites = (state: { threeD: ThreedState }) =>
  state.threeD.assaySuites;

export const selectAssayAttributes = (state: { threeD: ThreedState }) =>
  state.threeD.assayAttributes;

export const selectAssayPanelLoaded = (state: { threeD: ThreedState }) =>
  state.threeD.assayPanelLoaded;

export const selectAssayData = (state: { threeD: ThreedState }) =>
  state.threeD.assayData;

export default threedSlice;
