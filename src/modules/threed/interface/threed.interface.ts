// DrillHole data structure based on CSV format:
// HOLEID,PROJECTCODE,EAST,NORTH,RL,DEPTH

// Display mode for drillhole visualization
export type DrillholeDisplayMode = "drillhole" | "desurvey";

export interface DrillholeData {
  id: string;
  name: string;
  projectCode: string;
  east: number; // X coordinate
  north: number; // Y coordinate
  rl: number; // Z coordinate (Relative Level / elevation)
  depth: number; // Total depth of the hole
  dip: number; // Dip angle in degrees (90 = vertical down, 0 = horizontal)
  azimuth: number; // Azimuth in degrees (0/360 = North, 90 = East, 180 = South, 270 = West)
  // Additional fields for desurvey mode
  desurveyResults?: Array<{
    drillHoleId: number;
    depth: number;
    northing: number;
    easting: number;
    elevation: number;
    id: number;
  }>;
  // Additional fields from API response
  drillHoleStatus?: number;
  easting?: number | null;
  elevation?: number | null;
  isActive?: boolean;
  isExist?: boolean;
  isExport?: boolean;
  latitude?: number | null;
}

export interface GeologySegment {
  from: number;
  to: number;
  rockType: string;
  color: string;
  drillholeId: number;
  dataEntry?: DataEntryValue[]; // Add dataEntry to the interface
}

export interface GeologyData {
  drillholeId: number;
  segments: GeologySegment[];
}

export interface ThreedQuery {
  projectId?: string;
  page?: number;
  pageSize?: number;
}

// New interfaces for the real API integration
export interface RockStyle {
  name: string;
  fillColor: string;
  fillTexture: string;
  fillTransparency: number;
  lineColor: string;
  lineStyle: string;
  lineThickness: number;
  isActive: boolean;
  id: number;
}

export interface RockType {
  name: string;
  code: string;
  description: string;
  isActive: boolean;
  rockStyleId: number;
  rockStyle: RockStyle;
  id: number;
}

export interface DataEntryValue {
  valueId: number;
  geologysuiteFieldId: number;
  sequence: number;
  fieldName: string;
  fieldType: number;
  description: string | null;
  numberId: number | null;
  number: string | null;
  numberValue: number | null;
  rockTypeId: number | null;
  rockType: RockType | null;
  pickListItemId: number | null;
  pickListItem: any | null;
  colourId: number | null;
  colour: any | null;
  dateValue: string | null;
}

export interface DataEntry {
  geologySuiteId: number;
  drillholeId: number;
  depthFrom: number;
  depthTo: number;
  dataEntryValues: DataEntryValue[];
  id: number;
}

export interface DrillholeDataEntry {
  drillholeId: number;
  dataEntry: DataEntry[];
}

export interface GeologyApiResponse {
  result: {
    totalCount: number;
    items: DrillholeDataEntry[];
  };
  targetUrl: string | null;
  success: boolean;
  error: any | null;
  unAuthorizedRequest: boolean;
  __abp: boolean;
}
