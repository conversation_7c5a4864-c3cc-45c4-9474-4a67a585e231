import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import {
  DownOutlined,
  EditOutlined,
  ExportOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { Button, Dropdown, Popover, Switch, Tag, Tooltip } from "antd";
import { useCallback, useEffect, useState } from "react";
import { UseFieldArrayReturn, UseFormReturn } from "react-hook-form";
import * as XLSX from "xlsx";
import { setIsEditMode } from "../redux/importDataSlice/import-data.slice";
import { ModalFill } from "./modal-fill";
import { AzimuthRow } from "./render-row/azimuth-row";
import { DepthFromRow } from "./render-row/depth-from-row";
import { DepthRow } from "./render-row/depth-row";
import { DepthToRow } from "./render-row/depth-to-row";
import { DipsRow } from "./render-row/dip-row";
import { NameRow } from "./render-row/name-row";
import { RenderGeologySuite } from "./render-row/render-geology-suite";
import { TypeRow } from "./render-row/type-row";
import VirtualTable from "./virtual-table";

interface DataType {
  [key: string]: any;
}

interface FormTableProps {
  errorImport: any;
  handleDownload: () => void;
  gapError: Set<[number, number]>;
  overlapError: Set<[number, number]>;
  form: UseFormReturn<any>;
  fieldArray: UseFieldArrayReturn<any, "dataUpload", "id">;
}

export const FormTable = ({
  errorImport,
  handleDownload,
  gapError,
  overlapError,
  form,
  fieldArray,
}: FormTableProps) => {
  const [columns, setColumns] = useState<any[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedColumn, setSelectedColumn] = useState("");
  const dispatch = useAppDispatch();
  const { header, maxDepth, isEditMode, fileName, field } = useAppSelector(
    (state) => state.importData
  );

  const depthFromField = field.find((d) => d.systemFieldName === "Depth From");
  const depthToField = field.find((d) => d.systemFieldName === "Depth To");
  const fileColumnNameDepthFrom = depthFromField?.fileColumnName;
  const fileColumnNameDepthTo = depthToField?.fileColumnName;

  // Watch dataUpload changes
  const dataTable = form.watch("dataUpload") ?? [];

  const showFillModal = (keyName: string) => {
    setSelectedColumn(keyName);
    form.setValue("fillValue", "");
    setIsModalOpen(true);
  };

  const createColumns = (firstRow: DataType) => {
    if (!firstRow) return [];
    return Object.keys(firstRow)
      .filter((key) => key !== "key")
      .map((keyName) => {
        const keyfileColumnName = field?.find((d) => {
          return d.fileColumnName === keyName;
        });

        const systemFieldName = keyfileColumnName?.systemFieldName;

        const renderField = (text: string, record: DataType, index: number) => {
          if (!systemFieldName) {
            return (
              <Tooltip title={`Can't see ${keyName} in template`}>
                <Tag color="red">{record[keyName]}</Tag>
              </Tooltip>
            );
          }

          if (!isEditMode) {
            return <span>{record[keyName]}</span>;
          }

          switch (systemFieldName) {
            case "Type":
              return <TypeRow index={index} keyName={keyName} form={form} />;
            case "DrillHole":
              return <NameRow index={index} keyName={keyName} form={form} />;
            case "Drill Hole":
              return <NameRow index={index} keyName={keyName} form={form} />;
            case "Depth":
              return <DepthRow index={index} keyName={keyName} form={form} />;
            case "Depth From":
              return (
                <DepthFromRow
                  fileColumnNameDepthTo={fileColumnNameDepthTo}
                  index={index}
                  keyName={keyName}
                  form={form}
                />
              );
            case "Depth To":
              return (
                <DepthToRow
                  fileColumnNameDepthFrom={fileColumnNameDepthFrom}
                  index={index}
                  keyName={keyName}
                  form={form}
                />
              );
            case "Dip":
              return <DipsRow index={index} keyName={keyName} form={form} />;
            case "Depth (m)":
              return <DepthRow index={index} keyName={keyName} form={form} />;
            case "Azimuth":
              return <AzimuthRow index={index} keyName={keyName} form={form} />;
            default:
              return (
                <RenderGeologySuite
                  record={record}
                  keyName={keyName}
                  index={index}
                  form={form}
                />
              );
          }
        };

        const columnTitle = (
          <div className="flex items-center justify-between">
            <span>{keyName}</span>
            {isEditMode &&
              systemFieldName &&
              ![
                "DrillHole",
                "Drill Hole",
                "Depth",
                "Depth From",
                "Depth To",
              ].includes(systemFieldName) && (
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: "fill",
                        label: "Fill Empty Cells",
                        onClick: () => showFillModal(keyName),
                      },
                    ],
                  }}
                >
                  <Button type="text" size="small" icon={<DownOutlined />} />
                </Dropdown>
              )}
          </div>
        );

        return {
          title: columnTitle,
          dataIndex: keyName,
          width: 150,
          key: keyName,
          render: renderField,
        };
      });
  };

  useEffect(() => {
    if (form.watch("dataUpload")?.length > 0) {
      const firstRow = form.watch("dataUpload")[0];
      setColumns(createColumns(firstRow));
    }
  }, [
    isEditMode,
    form.watch("dataUpload"),
    maxDepth,
    form.watch("ImportMappingFields"),
    field,
  ]);

  // Add effect to reset form when dataUpload changes
  useEffect(() => {
    if (form.watch("dataUpload")?.length > 0) {
      // Reset form errors when new data is uploaded
      form.clearErrors();
    }
  }, [form.watch("dataUpload"), form]);

  const handleDownloadWithValidation = useCallback(() => {
    // Trigger validation for all fields before downloading
    form.trigger();

    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(dataTable);
    XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
    XLSX.writeFile(wb, fileName);
  }, [form, dataTable, fileName]);

  // Add effect to trigger validation when form state changes
  useEffect(() => {
    if (form.formState.isSubmitting) {
      form.trigger();
    }
  }, [form.formState.isSubmitting, form]);

  const headerTitle = "Alteration Description";
  const headerIndex = 0;

  return (
    <>
      <VirtualTable
        title={() => {
          return (
            <div className="grid grid-cols-12 gap-2 items-center">
              <p className="col-span-2">{fileName}</p>
              <div className="col-span-4">
                <div className="flex gap-2">
                  {gapError?.size > 0 && (
                    <Popover
                      placement="bottom"
                      title={<p>Gap Warning</p>}
                      content={
                        <div className="flex flex-col gap-2 max-h-[250px] overflow-y-auto">
                          {Array.from(gapError).map((d) => (
                            <p>
                              {d[0]}-{d[1]}
                            </p>
                          ))}
                        </div>
                      }
                    >
                      <Tag color="gold" className="cursor-pointer font-bold">
                        Gap Warning
                      </Tag>
                    </Popover>
                  )}
                  {overlapError?.size > 0 && (
                    <Popover
                      placement="bottom"
                      title={<p>Overlap Error</p>}
                      content={
                        <div className="flex flex-col gap-2 max-h-[250px] overflow-y-auto">
                          {Array.from(overlapError).map((d) => (
                            <p>
                              {d[0]}-{d[1]}
                            </p>
                          ))}
                        </div>
                      }
                    >
                      <Tag color="error" className="cursor-pointer font-bold">
                        Overlap Error
                      </Tag>
                    </Popover>
                  )}
                  {errorImport &&
                    Array.isArray(errorImport) &&
                    errorImport.length > 0 && (
                      <Popover
                        placement="rightTop"
                        title="Import Errors"
                        content={
                          <>
                            <div className="max-h-[200px] overflow-y-auto">
                              {errorImport.map((d) => {
                                return <p className="text-red-500">{d}</p>;
                              })}
                            </div>
                          </>
                        }
                      >
                        <Tag color="red">Import Errors</Tag>
                      </Popover>
                    )}
                </div>
              </div>
              <div className="col-span-5 flex gap-2 items-center">
                <Switch
                  checked={isEditMode}
                  onChange={() => {
                    dispatch(setIsEditMode(!isEditMode));
                  }}
                  checkedChildren={<EditOutlined />}
                  unCheckedChildren={<EyeOutlined />}
                />
              </div>
              <div className="col-span-1 flex gap-2 items-center justify-end">
                <p>{dataTable?.length} rows</p>
                <button
                  onClick={handleDownloadWithValidation}
                  disabled={dataTable?.length === 0}
                  className="flex items-center gap-2 p-1 bg-[#0F763D] text-white rounded-md hover:bg-[#0F763D]/80"
                >
                  <ExportOutlined />
                </button>
              </div>
            </div>
          );
        }}
        dataSource={dataTable}
        columns={columns}
        pagination={false}
        bordered
        scroll={{ x: "max-content", y: 6 * 70 }}
        isEditMode={isEditMode}
        formState={form.formState}
      />
      <ModalFill
        selectedColumn={selectedColumn}
        isModalOpen={isModalOpen}
        form={form}
        setIsModalOpen={setIsModalOpen}
      />
    </>
  );
};
