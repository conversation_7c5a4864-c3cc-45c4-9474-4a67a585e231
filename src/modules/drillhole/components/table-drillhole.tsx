"use client";
import { RequestState } from "@/common/configs/app.contants";
import { useAntdPagination } from "@/common/hooks/useAntdPagination";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import DrillHoleStatus from "@/components/common/drillhole-status";
import { ModalCommon } from "@/components/common/modal-common";
import { TableCommon } from "@/components/common/table-common";
import { IconSearch } from "@/components/icons";
import { selectDetailAccountSettings } from "@/modules/account-settings/redux/accountSettingsSlice";
import { EditOutlined, PlusOutlined } from "@ant-design/icons";
import { Select, Tag, Tooltip, type TableColumnsType } from "antd";
import { createStyles } from "antd-style";
import { debounce } from "lodash";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useEffect, useState } from "react";
import { CiViewTable } from "react-icons/ci";
import { FaRegEdit } from "react-icons/fa";
import { IoImageSharp } from "react-icons/io5";
import { drillHoleStatusOptions } from "../model/enum/drillhole.enum";
import { selectDrillholes } from "../redux/drillholeSlice";
import { getDrillholes } from "../redux/drillholeSlice/thunks";
import { DrillholeDetail } from "./drillhole-detail";
import { ModalDrillhole } from "./modal-drillhole";

const { Option } = Select;

const useStyle = createStyles(({ token, css, cx }) => {
  return {
    customTable: css`
      .ant-table {
        .ant-table-container {
          .ant-table-body,
          .ant-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
            max-height: 650px;
          }
        }
      }
    `,
  };
});

const TableDrillHole: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const _accountSettings = useAppSelector(selectDetailAccountSettings);
  const projectId = useAppSelector((state) => state.user.userInfo.projectId);
  const prospectId = useAppSelector((state) => state.user.userInfo.prospectId);
  const drillholes = useAppSelector(selectDrillholes);

  const [modalDataState, setModalDataState] = useState<any>({
    isOpen: false,
    data: null,
  });

  const { handleTableChange, tablePagination, refresh, queries } =
    useAntdPagination({
      reduxTableData: drillholes?.result?.result?.items ?? [],
      reduxTablePagination: {
        total: drillholes.result?.pagination?.total,
        pageSize: drillholes.result?.pagination?.pageSize,
      } as any,
      requestState: drillholes?.status ?? RequestState.idle,
      getDataAction: getDrillholes,
      filter: {
        projectId: projectId,
        prospectId: prospectId,
      },
      pageSize: 50,
    });

  //get all query params
  let querySearch: any;
  searchParams.forEach((value, key) => {
    querySearch = {
      ...querySearch,
      [key]: value,
    };
  });

  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });

  //table
  const columns: TableColumnsType<any> = [
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      width: 150,
      render: (_, record, index) => {
        return (
          <div className="flex gap-3 items-center">
            <Tooltip title="Edit">
              <EditOutlined
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "update",
                    detailInfo: record,
                  })
                }
                style={{ fontSize: 16 }}
                className="hover:text-primary cursor-pointer"
              />
            </Tooltip>
            <Tooltip title="View Drillhole data">
              <Link
                key={index}
                href={{
                  pathname: `/drillhole-management/${record.id}`,
                }}
                className="uppercase"
              >
                <CiViewTable style={{ fontSize: 16 }} />
              </Link>
            </Tooltip>
            <Tooltip title="View Images">
              <Link href={`/images?name=${record.name}`}>
                <IoImageSharp
                  style={{ fontSize: 16 }}
                  className="hover:text-primary cursor-pointer"
                />
              </Link>
            </Tooltip>
            <Tooltip title="Edit Log">
              <Link href={`/logging?drillholeId=${record.id}`}>
                <FaRegEdit />
              </Link>
            </Tooltip>
          </div>
        );
      },
    },

    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: 150,
      sorter: true,
      render(value, record, index) {
        return (
          <Link
            key={index}
            href={{
              pathname: `/drillhole-management/${record.id}`,
            }}
            className="uppercase"
          >
            {value}
          </Link>
        );
      },
    },
    {
      width: 150,

      title: "Project",
      dataIndex: "project",
      sorter: true,

      key: "project",
      render: (value, record, index) => (
        <Tag
          color={record?.project.backgroundColor}
          className={`min-w-[100px]`}
          style={{ color: record?.project.textColor }}
          key={index}
        >
          {value?.name}
        </Tag>
      ),
    },
    {
      title: "Prospect",
      dataIndex: "prospect",
      width: 150,

      key: "prospect",
      render: (value, record, index) => (
        <Tag
          style={{
            backgroundColor: record?.prospect?.backgroundColor,
            color: record?.prospect?.textColor,
          }}
        >
          {record?.prospect?.name}
        </Tag>
      ),
    },

    {
      title: "Status",
      dataIndex: "drillHoleStatus",
      sorter: true,
      width: 100,

      key: "drillHoleStatus",
      render: (drillHoleStatus) => {
        return <DrillHoleStatus status={drillHoleStatus} />;
      },
    },
    {
      title: "Date",
      dataIndex: "creationTime",
      sorter: true,
      width: 200,

      key: "creationTime",
      render(value, record, index) {
        return new Date(value).toLocaleString(); // Hiển thị cả ngày và giờ
      },
    },
    {
      title: "Original Images",
      width: 200,
      dataIndex: "originalImages",
    },
    {
      title: "Row Images",
      width: 200,
      dataIndex: "croppedRows",
      sorter: true,
    },
    {
      title: "Max Depth",
      width: 200,
      dataIndex: "maxDepth",
      sorter: true,

      key: "maxDepth",
    },
    {
      title: "Elevation",
      width: 100,
      dataIndex: "elevation",
      key: "elevation",
    },
    {
      title: "Northing",
      width: 100,
      dataIndex: "northing",
      key: "northing",
    },
    {
      title: "Easting",
      width: 100,
      dataIndex: "easting",
      key: "easting",
    },
    {
      title: "Dip",
      width: 100,
      dataIndex: "dip",
      key: "dip",
    },
    {
      title: "Azimuth",
      width: 100,
      dataIndex: "azimuth",
      key: "azimuth",
    },
    {
      title: "Key",
      width: 100,
      dataIndex: "id",
      key: "id",
    },
  ];

  const updateSearchParams = useCallback(
    debounce((keyword) => {
      const params = new URLSearchParams(queries);
      keyword ? params.set("keyword", keyword) : params.delete("keyword");
      params.set("page", "1");
      router.replace(`${window.location.pathname}?${params.toString()}`);
    }, 300),
    [queries, router]
  );

  const [drillholeSelected, setDrillholeSelected] = useState(
    isNaN(Number(queries.drillHoleStatus))
      ? null
      : Number(queries.drillHoleStatus)
  );

  let skipCount = Number(searchParams.get("skipCount")) || 1;
  let maxResultCount = Number(searchParams.get("maxResultCount")) || 10;
  let current = Math.ceil(skipCount / maxResultCount);
  if (skipCount % maxResultCount === 0) {
    current += 1;
  }
  const buttons = [
    {
      title: "Active",
      isActveString: "true",
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.set("isActive", "true");
        params.set("page", "1");
        params.delete("keyword");
        router.replace(`${window.location.pathname}?${params.toString()}`);
      },
    },
    {
      title: "Inactive",
      isActveString: "false",
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.set("isActive", "false");
        params.set("page", "1");
        params.delete("keyword");
        router.replace(`${window.location.pathname}?${params.toString()}`);
      },
    },
    {
      title: "All",
      isActveString: undefined,
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.delete("isActive");
        params.delete("Keyword");
        params.delete("skipCount");
        router.replace(`${window.location.pathname}?${params.toString()}`);
      },
    },
  ];

  const styles = useStyle();
  useEffect(() => {
    if (drillholes.result?.items.length === 0) {
      if (tablePagination.current && tablePagination.current - 1 > 0) {
        refresh({
          page: tablePagination.current - 1,
        });
        const params = new URLSearchParams(queries);
        params.set("page", (tablePagination.current - 1).toString());
        router.replace(`${window.location.pathname}?${params.toString()}`);
      }
    }
  }, [drillholes.result?.items]);

  return (
    <>
      {modalDataState.isOpen && (
        <ModalCommon
          width={1000}
          open={modalDataState.isOpen}
          centered
          padding={0}
          footer={null}
          onCancel={() =>
            setModalDataState({ ...modalDataState, isOpen: false })
          }
        >
          <DrillholeDetail id={modalDataState?.data?.id} isModal={true} />
        </ModalCommon>
      )}
      {modalState.isOpen && (
        <ModalDrillhole
          fetchListDrillhole={refresh}
          modalState={modalState}
          setModalState={setModalState}
        />
      )}
      <div className="flex flex-col gap-5">
        <p className="text-34-34 font-semibold font-dmSans">{`${
          _accountSettings?.collectionName
            ? _accountSettings?.collectionName
            : "Drill Holes"
        }`}</p>
        <hr />
        <div className="flex justify-between gap-5">
          <div className="flex gap-2">
            <div className=" px-5 rounded-lg flex items-center gap-2 h-[38px] bg-white border">
              <IconSearch />
              <input
                type="text"
                placeholder={`Search by ${
                  _accountSettings?.collectionName
                    ? _accountSettings?.collectionName
                    : "Drill Holes"
                } name`}
                className="w-[200px] font-normal font-dmSans outline-none text-primary placeholder:text-gray80"
                onChange={(e) => updateSearchParams(e.target.value)}
                defaultValue={queries.keyword}
              />
            </div>
            <div className=" flex gap-2">
              <p className="font-medium min-w-16 mt-2">Status</p>
              <Select
                value={drillholeSelected}
                onChange={(value) => {
                  setDrillholeSelected(value as any);
                  if (value) {
                    // set page to 1
                    const params = new URLSearchParams(queries);
                    params.set("page", "1");
                    params.set("DrillHoleStatus", value as any);
                    router.replace(
                      `${window.location.pathname}?${params.toString()}`
                    );
                  } else {
                    // set page to 1
                    const params = new URLSearchParams(queries);
                    params.set("page", "1");
                    params.delete("DrillHoleStatus");
                    router.replace(
                      `${window.location.pathname}?${params.toString()}`
                    );
                  }
                }}
                className="w-full h-full min-h-[38px]"
                placeholder={`Select a ${
                  _accountSettings?.collectionName
                    ? _accountSettings?.collectionName
                    : "Drillholes"
                } status`}
                filterOption={false}
                allowClear
              >
                {drillHoleStatusOptions.map((d: any) => (
                  <Option key={d.value} value={d.value}>
                    {d.label}
                  </Option>
                ))}
              </Select>
            </div>
          </div>
          <div className="flex gap-2">
            {buttons.map((button, index) => {
              let className: string = "";
              const isActiveSearchParam = searchParams.get("isActive");
              if (isActiveSearchParam === null && button.title === "All") {
                className = "btn-primary btn-active";
              }
              if (isActiveSearchParam === button.isActveString) {
                className = "btn-primary btn-active";
              }
              return (
                <button
                  key={index}
                  className={`btn btn-sm ${className}`}
                  onClick={button.onclick}
                >
                  {button.title}
                </button>
              );
            })}
          </div>
        </div>
        <TableCommon
          className={styles.styles.customTable}
          pagination={tablePagination}
          showSorterTooltip
          loading={drillholes.status === RequestState.pending}
          onChange={handleTableChange}
          columns={columns as any}
          dataSource={drillholes.result?.items}
          scroll={{ x: "max-content", y: 6 * 110 }}
          footer={() => (
            <div className="flex flex-col gap-2 my-2">
              <button
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "create",
                    detailInfo: undefined,
                  })
                }
                className="btn w-full bg-primary border-none hover:bg-primary-hover"
              >
                <PlusOutlined style={{ fontSize: "18px", color: "white" }} />
                <span className="font-bold uppercase text-white ">
                  {`Add a ${
                    _accountSettings?.collectionName
                      ? _accountSettings?.collectionName
                      : "Drillhole"
                  }`}
                </span>
              </button>
              <div className="text-center text-gray-500">
                Showing {drillholes.result?.items?.length || 0} records of{" "}
                {drillholes.result?.pagination?.total || 0}
              </div>
            </div>
          )}
        />
      </div>
    </>
  );
};

export default TableDrillHole;
